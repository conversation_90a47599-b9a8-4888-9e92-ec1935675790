# Tulia - Complete Food Delivery System

A comprehensive full-stack food delivery application built with React Native (Expo) frontend and NestJS backend, featuring complete user management, admin dashboard, and GPS delivery tracking.

## 🚀 Features Overview

### ✅ **Complete Profile Management System**
- **User Profile Updates** - Edit name, phone, avatar URL with real-time validation
- **Secure Password Management** - Change password with current password verification
- **Address Management** - Add, edit, delete delivery addresses with location picker
- **Settings & Preferences** - Comprehensive user settings with notifications, security, and app preferences

### ✅ **Full Admin Dashboard**
- **Analytics & Reporting** - Dashboard with sales, user, and product analytics
- **User Management** - Search, filter, activate/deactivate users with role-based access
- **Product Management** - Complete CRUD operations for products with categories
- **Order Management** - Search and manage orders with status tracking
- **System Monitoring** - Health checks and system status monitoring

### ✅ **GPS Delivery Tracking System**
- **Real-time Location Tracking** - Live driver location updates with map integration
- **Delivery Management** - Complete delivery lifecycle from assignment to completion
- **Interactive Maps** - MapView integration with route visualization
- **Status Updates** - Real-time delivery status updates for customers and drivers
- **Location Services** - GPS permissions, geocoding, and distance calculations

## 🏗️ Architecture

### Frontend (React Native + Expo)
```
app/
├── (tabs)/           # Main tab navigation
│   ├── index.tsx     # Home screen
│   ├── menu.tsx      # Menu/products screen
│   ├── orders.tsx    # Orders screen
│   └── profile.tsx   # Profile screen
├── profile/          # Profile management screens
│   ├── edit.tsx      # Edit profile
│   ├── change-password.tsx
│   ├── addresses.tsx
│   └── settings.tsx
├── admin/            # Admin dashboard screens
│   ├── dashboard.tsx # Admin dashboard
│   ├── users.tsx     # User management
│   ├── analytics.tsx # Analytics
│   └── products.tsx  # Product management
├── delivery/         # GPS delivery screens
│   ├── track/[id].tsx # Delivery tracking
│   └── history.tsx   # Delivery history
└── driver/           # Driver app screens
    └── dashboard.tsx # Driver dashboard
```

### Backend (NestJS)
```
backend/src/
├── auth/             # Authentication & JWT
├── users/            # User management + profiles
├── admin/            # Admin dashboard APIs
├── delivery/         # GPS delivery tracking
├── products/         # Product management
├── orders/           # Order processing
├── payments/         # Payment integration
├── addresses/        # Address management
└── delivery-zones/   # Geographic zones
```

## 🛠️ Technology Stack

### Frontend
- **React Native** with Expo SDK
- **TypeScript** for type safety
- **Expo Router** for navigation
- **React Native Maps** for GPS functionality
- **Expo Location** for location services
- **Lucide React Native** for icons
- **Context API** for state management

### Backend
- **NestJS** framework
- **TypeScript** throughout
- **PostgreSQL** database
- **TypeORM** for database management
- **JWT** authentication
- **Swagger** API documentation
- **bcryptjs** for password hashing

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL database
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator or Android Emulator (or physical device)

### Backend Setup

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

4. **Start the backend:**
   ```bash
   npm run start:dev
   ```

   Backend will be available at `http://localhost:3000`

### Frontend Setup

1. **Navigate to project root:**
   ```bash
   cd ..
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the Expo development server:**
   ```bash
   npx expo start
   ```

4. **Run on device/simulator:**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Scan QR code with Expo Go app on physical device

## 📱 Application Features

### User Features
- **Authentication** - Register, login, logout with JWT tokens
- **Profile Management** - Complete profile editing with avatar support
- **Product Browsing** - Browse products by category with search
- **Order Management** - Place orders, track status, view history
- **Address Management** - Save multiple delivery addresses with GPS
- **Delivery Tracking** - Real-time GPS tracking of deliveries
- **Settings** - Comprehensive app settings and preferences

### Admin Features
- **Dashboard Analytics** - Sales, user, and product analytics
- **User Management** - Search, filter, and manage all users
- **Product Management** - Add, edit, delete products with categories
- **Order Management** - View and manage all orders
- **System Monitoring** - Health checks and system status
- **Delivery Management** - Assign and track deliveries

### Driver Features
- **Driver Dashboard** - View assigned deliveries
- **Location Tracking** - Real-time location updates
- **Status Updates** - Update delivery status (picked up, in transit, delivered)
- **Customer Communication** - Contact customers for delivery

## 🗄️ Database Schema

### Core Entities
- **Users** - Authentication, profiles, roles (user/admin)
- **Products** - Product catalog with categories and inventory
- **Orders** - Order management with items and status tracking
- **Addresses** - User delivery addresses with GPS coordinates
- **Deliveries** - GPS tracking with driver assignments
- **Payments** - Payment processing and status
- **Delivery Zones** - Geographic zones with delivery fees

## 🔐 Authentication & Security

- **JWT-based Authentication** - Secure token-based auth
- **Role-based Access Control** - User and admin roles
- **Password Security** - bcrypt hashing with salt
- **API Protection** - Protected routes with guards
- **Input Validation** - Comprehensive validation on all inputs

## 📍 GPS & Location Features

### Location Services
- **GPS Permissions** - Request and manage location permissions
- **Current Location** - Get user's current GPS coordinates
- **Geocoding** - Convert addresses to coordinates and vice versa
- **Distance Calculation** - Calculate distances between locations
- **Real-time Tracking** - Live location updates for deliveries

### Map Integration
- **Interactive Maps** - React Native Maps integration
- **Custom Markers** - Pickup, delivery, and driver location markers
- **Route Visualization** - Show delivery routes on map
- **Location Picker** - Interactive location selection
- **Address Search** - Search for locations by address

## 🎨 UI/UX Features

### Design System
- **Consistent Colors** - Unified color scheme throughout
- **Typography** - Consistent font sizes and weights
- **Spacing** - Standardized spacing system
- **Components** - Reusable UI components
- **Icons** - Lucide React Native icon library

### User Experience
- **Loading States** - Proper loading indicators
- **Error Handling** - User-friendly error messages
- **Empty States** - Helpful empty state screens
- **Responsive Design** - Works on various screen sizes
- **Smooth Navigation** - Expo Router navigation

## 🧪 Testing

### Backend Testing
```bash
cd backend
npm run test          # Unit tests
npm run test:e2e      # End-to-end tests
npm run test:cov      # Coverage report
```

### Frontend Testing
```bash
npm run test          # Run tests (when configured)
```

## 📚 API Documentation

- **Swagger UI** - Available at `http://localhost:3000/api/docs`
- **Comprehensive Endpoints** - All APIs documented with examples
- **Authentication** - JWT token requirements clearly marked
- **Request/Response** - Full request and response schemas

## 🚀 Deployment

### Backend Deployment
- **Docker Ready** - Dockerfile included
- **Environment Configuration** - Production environment variables
- **Database Migrations** - Automated schema updates
- **Health Checks** - Built-in health check endpoints

### Frontend Deployment
- **Expo Build** - Build for iOS and Android
- **Over-the-Air Updates** - Expo OTA updates
- **App Store Ready** - Production build configuration

## 📈 Performance Features

- **Optimized Queries** - Efficient database queries
- **Pagination** - Paginated API responses
- **Image Optimization** - Optimized image loading
- **Caching** - Strategic caching implementation
- **Real-time Updates** - Efficient real-time data updates

## 🔧 Development Tools

- **TypeScript** - Full type safety
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Hot Reload** - Development hot reload
- **Debugging** - Comprehensive debugging support

## 📱 Supported Platforms

- **iOS** - iPhone and iPad
- **Android** - Android phones and tablets
- **Web** - Expo web support (limited)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Review API documentation at `/api/docs`
- Check existing issues
- Create a new issue if needed

---

**Tulia** - Complete food delivery system with GPS tracking, admin dashboard, and comprehensive user management. Built with modern technologies for scalability and performance.
