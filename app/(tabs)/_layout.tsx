import { Tabs } from 'expo-router';
import { Chrome as Home, ShoppingBag, ShoppingCart, Package, User } from 'lucide-react-native';
import { colors } from '../../constants/colors';
import { useCart } from '../../context/CartContext';
import { View, Text, StyleSheet } from 'react-native';

function TabBarIcon({ name, color, focused }: { name: string; color: string; focused: boolean }) {
  const size = focused ? 24 : 20;
  
  switch (name) {
    case 'index':
      return <Home size={size} color={color} />;
    case 'products':
      return <ShoppingBag size={size} color={color} />;
    case 'cart':
      return <ShoppingCart size={size} color={color} />;
    case 'orders':
      return <Package size={size} color={color} />;
    case 'profile':
      return <User size={size} color={color} />;
    default:
      return null;
  }
}

function CartTabBarIcon({ color, focused }: { color: string; focused: boolean }) {
  const { itemCount } = useCart();
  
  return (
    <View style={styles.cartIconContainer}>
      <TabBarIcon name="cart" color={color} focused={focused} />
      {itemCount > 0 && (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>{itemCount > 99 ? '99+' : itemCount}</Text>
        </View>
      )}
    </View>
  );
}

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textMuted,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopWidth: 1,
          borderTopColor: colors.border,
          height: 80,
          paddingBottom: 20,
          paddingTop: 10,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon name="index" color={color} focused={focused} />
          ),
        }}
      />
      <Tabs.Screen
        name="products"
        options={{
          title: 'Products',
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon name="products" color={color} focused={focused} />
          ),
        }}
      />
      <Tabs.Screen
        name="cart"
        options={{
          title: 'Cart',
          tabBarIcon: ({ color, focused }) => (
            <CartTabBarIcon color={color} focused={focused} />
          ),
        }}
      />
      <Tabs.Screen
        name="orders"
        options={{
          title: 'Orders',
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon name="orders" color={color} focused={focused} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon name="profile" color={color} focused={focused} />
          ),
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  cartIconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: colors.white,
    fontSize: 10,
    fontWeight: '600',
  },
});