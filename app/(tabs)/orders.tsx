import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Package, Clock, CircleCheck as CheckCircle, Circle as XCircle } from 'lucide-react-native';
import { colors, spacing, borderRadius } from '../../constants/colors';
import { useAuth } from '../../context/AuthContext';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { Button } from '../../components/Button';
import { Order } from '../../types';
import { apiService } from '../../services/api';
import { storageService } from '../../services/storage';

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending':
    case 'processing':
      return <Clock size={20} color={colors.warning} />;
    case 'shipped':
    case 'delivered':
      return <CheckCircle size={20} color={colors.success} />;
    case 'cancelled':
      return <XCircle size={20} color={colors.error} />;
    default:
      return <Package size={20} color={colors.textMuted} />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
    case 'processing':
      return colors.warning;
    case 'shipped':
    case 'delivered':
      return colors.success;
    case 'cancelled':
      return colors.error;
    default:
      return colors.textMuted;
  }
};

export default function OrdersScreen() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated) {
      loadOrders();
    } else {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const loadOrders = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      // Load from cache first
      if (!isRefresh) {
        const cachedOrders = await storageService.getCachedOrders();
        if (cachedOrders.length > 0) {
          setOrders(cachedOrders);
        }
      }

      // Fetch fresh data
      const response = await apiService.getOrders();
      if (response.success && response.data) {
        setOrders(response.data);
        await storageService.cacheOrders(response.data);
      } else {
        setError(response.error || 'Failed to load orders');
      }
    } catch (err) {
      setError('Network error. Please check your connection.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleOrderPress = (order: Order) => {
    router.push(`/order/${order.id}`);
  };

  const handleLogin = () => {
    router.push('/auth/login');
  };

  const renderOrderItem = ({ item }: { item: Order }) => (
    <TouchableOpacity
      style={styles.orderCard}
      onPress={() => handleOrderPress(item)}
    >
      <View style={styles.orderHeader}>
        <Text style={styles.orderId}>Order #{item.id.slice(-8)}</Text>
        <View style={styles.statusContainer}>
          {getStatusIcon(item.status)}
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </Text>
        </View>
      </View>
      
      <Text style={styles.orderDate}>
        {new Date(item.createdAt).toLocaleDateString()}
      </Text>
      
      <View style={styles.orderDetails}>
        <Text style={styles.itemCount}>
          {item.items.length} item{item.items.length > 1 ? 's' : ''}
        </Text>
        <Text style={styles.orderAmount}>
          ${item.finalAmount.toFixed(2)}
        </Text>
      </View>
      
      <View style={styles.orderFooter}>
        <Text style={styles.deliveryType}>
          {item.deliveryType === 'delivery' ? 'Delivery' : 'Pickup'}
        </Text>
        <Text style={styles.paymentStatus}>
          Payment: {item.paymentStatus}
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Orders</Text>
        </View>
        <View style={styles.loginContainer}>
          <Package size={64} color={colors.textMuted} />
          <Text style={styles.loginTitle}>Login to view your orders</Text>
          <Text style={styles.loginSubtitle}>
            Sign in to track your orders and purchase history
          </Text>
          <Button
            title="Login"
            onPress={handleLogin}
            style={styles.loginButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  if (loading && orders.length === 0) {
    return <LoadingSpinner text="Loading orders..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Orders</Text>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button title="Retry" onPress={() => loadOrders()} size="sm" />
        </View>
      )}

      {orders.length === 0 && !loading && !error && (
        <View style={styles.emptyContainer}>
          <Package size={64} color={colors.textMuted} />
          <Text style={styles.emptyTitle}>No orders yet</Text>
          <Text style={styles.emptySubtitle}>
            Your order history will appear here
          </Text>
          <Button
            title="Start Shopping"
            onPress={() => router.push('/products')}
            style={styles.startShoppingButton}
          />
        </View>
      )}

      <FlatList
        data={orders}
        keyExtractor={(item) => item.id}
        renderItem={renderOrderItem}
        contentContainerStyle={styles.ordersList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => loadOrders(true)}
            colors={[colors.primary]}
          />
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text,
  },
  loginContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  loginTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  loginSubtitle: {
    fontSize: 14,
    color: colors.textMuted,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  loginButton: {
    paddingHorizontal: spacing.xl,
  },
  ordersList: {
    padding: spacing.lg,
  },
  orderCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  orderId: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: spacing.xs,
    textTransform: 'capitalize',
  },
  orderDate: {
    fontSize: 12,
    color: colors.textMuted,
    marginBottom: spacing.md,
  },
  orderDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  itemCount: {
    fontSize: 14,
    color: colors.textMuted,
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  deliveryType: {
    fontSize: 12,
    color: colors.textMuted,
  },
  paymentStatus: {
    fontSize: 12,
    color: colors.textMuted,
    textTransform: 'capitalize',
  },
  errorContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.textMuted,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  startShoppingButton: {
    paddingHorizontal: spacing.xl,
  },
});