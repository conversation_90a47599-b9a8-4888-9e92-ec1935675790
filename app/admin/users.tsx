import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import {
  ArrowLeft,
  Search,
  Filter,
  User,
  Mail,
  Phone,
  Shield,
  MoreVertical,
  UserCheck,
  UserX,
} from 'lucide-react-native';
import { colors, spacing, borderRadius } from '../../constants/colors';
import { useAuth } from '../../context/AuthContext';
import { apiService } from '../../services/api';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { User as UserType } from '../../types';

export default function AdminUsersScreen() {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState<UserType[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (currentUser?.role !== 'admin') {
      Alert.alert('Access Denied', 'You do not have permission to access this page', [
        { text: 'OK', onPress: () => router.back() },
      ]);
      return;
    }
    loadUsers();
  }, [currentUser, searchQuery, selectedRole]);

  const loadUsers = async (pageNum: number = 1, append: boolean = false) => {
    try {
      const response = await apiService.searchUsers(
        searchQuery || undefined,
        selectedRole || undefined,
        pageNum,
        20
      );

      if (response.success && response.data) {
        const newUsers = response.data.users;
        setUsers(append ? [...users, ...newUsers] : newUsers);
        setHasMore(pageNum < response.data.totalPages);
        setPage(pageNum);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    setPage(1);
    await loadUsers(1, false);
    setRefreshing(false);
  };

  const loadMore = () => {
    if (hasMore && !loading) {
      loadUsers(page + 1, true);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPage(1);
    setLoading(true);
  };

  const handleRoleFilter = (role: string) => {
    setSelectedRole(selectedRole === role ? '' : role);
    setPage(1);
    setLoading(true);
  };

  const handleUserAction = (user: UserType, action: string) => {
    switch (action) {
      case 'view':
        Alert.alert('User Details', `Name: ${user.name}\nEmail: ${user.email}\nPhone: ${user.phone}\nRole: ${user.role}`);
        break;
      case 'activate':
        Alert.alert('Activate User', `Are you sure you want to activate ${user.name}?`, [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Activate', onPress: () => updateUserStatus(user.id, true) },
        ]);
        break;
      case 'deactivate':
        Alert.alert('Deactivate User', `Are you sure you want to deactivate ${user.name}?`, [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Deactivate', onPress: () => updateUserStatus(user.id, false) },
        ]);
        break;
      default:
        break;
    }
  };

  const updateUserStatus = async (userId: string, isActive: boolean) => {
    try {
      const response = await apiService.updateUserStatus(userId, isActive);
      if (response.success) {
        Alert.alert('Success', `User ${isActive ? 'activated' : 'deactivated'} successfully`);
        onRefresh();
      } else {
        Alert.alert('Error', response.error || 'Failed to update user status');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update user status');
    }
  };

  const renderUserCard = (user: UserType) => (
    <View key={user.id} style={styles.userCard}>
      <View style={styles.userHeader}>
        <View style={styles.userAvatar}>
          <User size={20} color={colors.white} />
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{user.name}</Text>
          <Text style={styles.userEmail}>{user.email}</Text>
          <Text style={styles.userPhone}>{user.phone}</Text>
        </View>
        <View style={styles.userActions}>
          <View style={[styles.roleBadge, user.role === 'admin' ? styles.adminBadge : styles.userBadge]}>
            <Shield size={12} color={user.role === 'admin' ? colors.error : colors.primary} />
            <Text style={[styles.roleText, user.role === 'admin' ? styles.adminText : styles.userText]}>
              {user.role}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              Alert.alert('User Actions', 'Choose an action:', [
                { text: 'View Details', onPress: () => handleUserAction(user, 'view') },
                { text: 'Activate', onPress: () => handleUserAction(user, 'activate') },
                { text: 'Deactivate', onPress: () => handleUserAction(user, 'deactivate') },
                { text: 'Cancel', style: 'cancel' },
              ]);
            }}
          >
            <MoreVertical size={16} color={colors.textMuted} />
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.userMeta}>
        <Text style={styles.userDate}>
          Joined: {new Date(user.createdAt).toLocaleDateString()}
        </Text>
      </View>
    </View>
  );

  if (loading && users.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>User Management</Text>
          <View style={styles.placeholder} />
        </View>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>User Management</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Search and Filters */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Search size={20} color={colors.textMuted} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search users..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor={colors.textMuted}
          />
        </View>
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[styles.filterButton, selectedRole === 'user' && styles.filterButtonActive]}
            onPress={() => handleRoleFilter('user')}
          >
            <Text style={[styles.filterText, selectedRole === 'user' && styles.filterTextActive]}>
              Users
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterButton, selectedRole === 'admin' && styles.filterButtonActive]}
            onPress={() => handleRoleFilter('admin')}
          >
            <Text style={[styles.filterText, selectedRole === 'admin' && styles.filterTextActive]}>
              Admins
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
          if (isCloseToBottom) {
            loadMore();
          }
        }}
        scrollEventThrottle={400}
      >
        {users.length === 0 ? (
          <View style={styles.emptyState}>
            <User size={64} color={colors.textMuted} />
            <Text style={styles.emptyTitle}>No Users Found</Text>
            <Text style={styles.emptySubtitle}>
              {searchQuery ? 'Try adjusting your search criteria' : 'No users available'}
            </Text>
          </View>
        ) : (
          <View style={styles.usersList}>
            {users.map(renderUserCard)}
            {loading && (
              <View style={styles.loadingMore}>
                <LoadingSpinner size="small" />
              </View>
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.xs,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  placeholder: {
    width: 32,
  },
  searchSection: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  searchInput: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingLeft: spacing.sm,
    fontSize: 16,
    color: colors.text,
  },
  filterContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  filterButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  filterButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterText: {
    fontSize: 12,
    color: colors.textMuted,
    fontWeight: '500',
  },
  filterTextActive: {
    color: colors.white,
  },
  content: {
    flex: 1,
  },
  usersList: {
    padding: spacing.lg,
  },
  userCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  userEmail: {
    fontSize: 14,
    color: colors.textMuted,
    marginBottom: spacing.xs,
  },
  userPhone: {
    fontSize: 14,
    color: colors.textMuted,
  },
  userActions: {
    alignItems: 'flex-end',
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
    marginBottom: spacing.sm,
  },
  adminBadge: {
    backgroundColor: colors.error + '20',
  },
  userBadge: {
    backgroundColor: colors.primary + '20',
  },
  roleText: {
    fontSize: 10,
    fontWeight: '500',
    marginLeft: spacing.xs,
    textTransform: 'uppercase',
  },
  adminText: {
    color: colors.error,
  },
  userText: {
    color: colors.primary,
  },
  actionButton: {
    padding: spacing.sm,
  },
  userMeta: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.sm,
  },
  userDate: {
    fontSize: 12,
    color: colors.textMuted,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.textMuted,
    textAlign: 'center',
  },
  loadingMore: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },
});
