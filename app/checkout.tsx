import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import {
  ArrowLeft,
  MapPin,
  CreditCard,
  Phone,
  Truck,
  Package,
  Plus,
  Minus,
  ShoppingCart,
} from 'lucide-react-native';
import { colors, spacing, borderRadius } from '../constants/colors';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/api';
import { LoadingSpinner } from '../components/LoadingSpinner';
import { Button } from '../components/Button';

interface CartItem {
  productId: string;
  product: {
    id: string;
    name: string;
    price: number;
    imageUrl: string;
    category: string;
  };
  quantity: number;
  price: number;
  total: number;
}

interface Address {
  id: string;
  name: string;
  address: string;
  city: string;
  phone: string;
  isDefault: boolean;
}

export default function CheckoutScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const params = useLocalSearchParams();
  
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [deliveryType, setDeliveryType] = useState<'pickup' | 'delivery'>('delivery');
  const [paymentMethod, setPaymentMethod] = useState<'mtn' | 'airtel'>('mtn');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [orderSummary, setOrderSummary] = useState<any>(null);

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (cartItems.length > 0) {
      calculateOrderSummary();
    }
  }, [cartItems, deliveryType, selectedAddress]);

  const loadInitialData = async () => {
    try {
      // Load cart items from params or storage
      const items = params.items ? JSON.parse(params.items as string) : [];
      setCartItems(items);

      // Load user addresses
      const addressResponse = await apiService.getAddresses();
      if (addressResponse.success && addressResponse.data) {
        setAddresses(addressResponse.data);
        const defaultAddress = addressResponse.data.find((addr: Address) => addr.isDefault);
        if (defaultAddress) {
          setSelectedAddress(defaultAddress);
        }
      }

      // Set default phone number
      if (user?.phone) {
        setPhoneNumber(user.phone);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load checkout data');
    } finally {
      setLoading(false);
    }
  };

  const calculateOrderSummary = async () => {
    try {
      const checkoutItems = cartItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
      }));

      const response = await apiService.getOrderSummary(checkoutItems);
      if (response.success) {
        setOrderSummary(response.data);
      }
    } catch (error) {
      console.error('Failed to calculate order summary:', error);
    }
  };

  const updateQuantity = (productId: string, change: number) => {
    setCartItems(prev => prev.map(item => {
      if (item.productId === productId) {
        const newQuantity = Math.max(1, item.quantity + change);
        return {
          ...item,
          quantity: newQuantity,
          total: item.price * newQuantity,
        };
      }
      return item;
    }));
  };

  const removeItem = (productId: string) => {
    setCartItems(prev => prev.filter(item => item.productId !== productId));
  };

  const processCheckout = async () => {
    if (cartItems.length === 0) {
      Alert.alert('Error', 'Your cart is empty');
      return;
    }

    if (deliveryType === 'delivery' && !selectedAddress) {
      Alert.alert('Error', 'Please select a delivery address');
      return;
    }

    if (!phoneNumber.trim()) {
      Alert.alert('Error', 'Please enter your phone number');
      return;
    }

    setProcessing(true);

    try {
      const checkoutData = {
        items: cartItems.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
        })),
        addressId: selectedAddress?.id,
        deliveryType,
        paymentMethod,
        phoneNumber: phoneNumber.trim(),
        notes: notes.trim(),
      };

      const response = await apiService.processCheckout(checkoutData);
      
      if (response.success) {
        Alert.alert(
          'Order Placed Successfully!',
          `Your order has been placed. Payment reference: ${response.data.payment.referenceId}`,
          [
            {
              text: 'View Order',
              onPress: () => router.push(`/orders/${response.data.order.id}`),
            },
          ]
        );
      } else {
        Alert.alert('Error', response.message || 'Failed to process checkout');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to process checkout. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
    }).format(amount);
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  if (cartItems.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Checkout</Text>
          <View style={styles.placeholder} />
        </View>
        
        <View style={styles.emptyState}>
          <ShoppingCart size={64} color={colors.textMuted} />
          <Text style={styles.emptyTitle}>Your cart is empty</Text>
          <Text style={styles.emptySubtitle}>
            Add some items to your cart to proceed with checkout
          </Text>
          <Button
            title="Continue Shopping"
            onPress={() => router.push('/menu')}
            style={styles.continueButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Checkout</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Cart Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Items</Text>
          {cartItems.map((item) => (
            <View key={item.productId} style={styles.cartItem}>
              <View style={styles.itemInfo}>
                <Text style={styles.itemName}>{item.product.name}</Text>
                <Text style={styles.itemPrice}>{formatCurrency(item.price)}</Text>
              </View>
              
              <View style={styles.quantityControls}>
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => updateQuantity(item.productId, -1)}
                >
                  <Minus size={16} color={colors.primary} />
                </TouchableOpacity>
                
                <Text style={styles.quantity}>{item.quantity}</Text>
                
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => updateQuantity(item.productId, 1)}
                >
                  <Plus size={16} color={colors.primary} />
                </TouchableOpacity>
              </View>
              
              <Text style={styles.itemTotal}>{formatCurrency(item.total)}</Text>
            </View>
          ))}
        </View>

        {/* Delivery Type */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Option</Text>
          <View style={styles.deliveryOptions}>
            <TouchableOpacity
              style={[
                styles.deliveryOption,
                deliveryType === 'delivery' && styles.selectedOption,
              ]}
              onPress={() => setDeliveryType('delivery')}
            >
              <Truck size={20} color={deliveryType === 'delivery' ? colors.primary : colors.textMuted} />
              <Text style={[
                styles.optionText,
                deliveryType === 'delivery' && styles.selectedOptionText,
              ]}>
                Delivery
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.deliveryOption,
                deliveryType === 'pickup' && styles.selectedOption,
              ]}
              onPress={() => setDeliveryType('pickup')}
            >
              <Package size={20} color={deliveryType === 'pickup' ? colors.primary : colors.textMuted} />
              <Text style={[
                styles.optionText,
                deliveryType === 'pickup' && styles.selectedOptionText,
              ]}>
                Pickup
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Delivery Address */}
        {deliveryType === 'delivery' && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Delivery Address</Text>
              <TouchableOpacity
                onPress={() => router.push('/profile/addresses')}
              >
                <Text style={styles.addButton}>Add New</Text>
              </TouchableOpacity>
            </View>
            
            {addresses.map((address) => (
              <TouchableOpacity
                key={address.id}
                style={[
                  styles.addressOption,
                  selectedAddress?.id === address.id && styles.selectedAddress,
                ]}
                onPress={() => setSelectedAddress(address)}
              >
                <MapPin size={16} color={colors.primary} />
                <View style={styles.addressInfo}>
                  <Text style={styles.addressName}>{address.name}</Text>
                  <Text style={styles.addressText}>{address.address}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Payment Method */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          <View style={styles.paymentOptions}>
            <TouchableOpacity
              style={[
                styles.paymentOption,
                paymentMethod === 'mtn' && styles.selectedOption,
              ]}
              onPress={() => setPaymentMethod('mtn')}
            >
              <CreditCard size={20} color={paymentMethod === 'mtn' ? colors.primary : colors.textMuted} />
              <Text style={[
                styles.optionText,
                paymentMethod === 'mtn' && styles.selectedOptionText,
              ]}>
                MTN Mobile Money
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.paymentOption,
                paymentMethod === 'airtel' && styles.selectedOption,
              ]}
              onPress={() => setPaymentMethod('airtel')}
            >
              <CreditCard size={20} color={paymentMethod === 'airtel' ? colors.primary : colors.textMuted} />
              <Text style={[
                styles.optionText,
                paymentMethod === 'airtel' && styles.selectedOptionText,
              ]}>
                Airtel Money
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Phone Number */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Phone Number</Text>
          <View style={styles.phoneInput}>
            <Phone size={20} color={colors.textMuted} />
            <TextInput
              style={styles.phoneTextInput}
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              placeholder="Enter phone number"
              keyboardType="phone-pad"
              placeholderTextColor={colors.textMuted}
            />
          </View>
        </View>

        {/* Notes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Special Instructions (Optional)</Text>
          <TextInput
            style={styles.notesInput}
            value={notes}
            onChangeText={setNotes}
            placeholder="Any special instructions for your order..."
            multiline
            numberOfLines={3}
            placeholderTextColor={colors.textMuted}
          />
        </View>

        {/* Order Summary */}
        {orderSummary && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Order Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal</Text>
              <Text style={styles.summaryValue}>
                {formatCurrency(orderSummary.summary.totalAmount)}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Delivery Fee</Text>
              <Text style={styles.summaryValue}>
                {formatCurrency(orderSummary.summary.deliveryFee)}
              </Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>
                {formatCurrency(orderSummary.summary.finalAmount)}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Checkout Button */}
      <View style={styles.checkoutSection}>
        <Button
          title={processing ? 'Processing...' : 'Place Order'}
          onPress={processCheckout}
          disabled={processing}
          style={styles.checkoutButton}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.xs,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.md,
  },
  addButton: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  cartItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  itemPrice: {
    fontSize: 12,
    color: colors.textMuted,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: spacing.md,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  quantity: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginHorizontal: spacing.md,
    minWidth: 30,
    textAlign: 'center',
  },
  itemTotal: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  deliveryOptions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  deliveryOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.background,
  },
  selectedOption: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  optionText: {
    fontSize: 14,
    color: colors.textMuted,
    marginLeft: spacing.sm,
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: '500',
  },
  addressOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.background,
    marginBottom: spacing.sm,
  },
  selectedAddress: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  addressInfo: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  addressName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  addressText: {
    fontSize: 12,
    color: colors.textMuted,
  },
  paymentOptions: {
    gap: spacing.md,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.background,
  },
  phoneInput: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    backgroundColor: colors.background,
  },
  phoneTextInput: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingLeft: spacing.sm,
    fontSize: 16,
    color: colors.text,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    fontSize: 16,
    color: colors.text,
    backgroundColor: colors.background,
    textAlignVertical: 'top',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  summaryLabel: {
    fontSize: 14,
    color: colors.textMuted,
  },
  summaryValue: {
    fontSize: 14,
    color: colors.text,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    marginTop: spacing.sm,
    paddingTop: spacing.md,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  totalValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
  },
  checkoutSection: {
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  checkoutButton: {
    paddingVertical: spacing.md,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.textMuted,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  continueButton: {
    paddingHorizontal: spacing.xl,
  },
});
