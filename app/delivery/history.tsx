import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import {
  ArrowLeft,
  Package,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  Navigation,
  ChevronRight,
} from 'lucide-react-native';
import { colors, spacing, borderRadius } from '../../constants/colors';
import { useAuth } from '../../context/AuthContext';
import { apiService } from '../../services/api';
import { LoadingSpinner } from '../../components/LoadingSpinner';

interface Delivery {
  id: string;
  status: string;
  pickupAddress: string;
  deliveryAddress: string;
  estimatedDeliveryTime: string;
  deliveredAt?: string;
  createdAt: string;
  driver: {
    name: string;
    phone: string;
  };
  order: {
    id: string;
    finalAmount: number;
  };
}

export default function DeliveryHistoryScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [deliveries, setDeliveries] = useState<Delivery[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDeliveries();
  }, []);

  const loadDeliveries = async () => {
    try {
      const response = await apiService.getMyDeliveries();
      if (response.success && response.data) {
        setDeliveries(response.data);
      } else {
        Alert.alert('Error', 'Failed to load delivery history');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load delivery history');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDeliveries();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned':
        return colors.warning;
      case 'picked_up':
        return colors.primary;
      case 'in_transit':
        return colors.primary;
      case 'delivered':
        return colors.success;
      case 'failed':
        return colors.error;
      default:
        return colors.textMuted;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Clock size={16} color={colors.warning} />;
      case 'picked_up':
        return <Package size={16} color={colors.primary} />;
      case 'in_transit':
        return <Navigation size={16} color={colors.primary} />;
      case 'delivered':
        return <CheckCircle size={16} color={colors.success} />;
      case 'failed':
        return <AlertCircle size={16} color={colors.error} />;
      default:
        return <Clock size={16} color={colors.textMuted} />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-UG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderDeliveryCard = (delivery: Delivery) => (
    <TouchableOpacity
      key={delivery.id}
      style={styles.deliveryCard}
      onPress={() => router.push(`/delivery/track/${delivery.id}`)}
    >
      <View style={styles.deliveryHeader}>
        <View style={styles.statusContainer}>
          {getStatusIcon(delivery.status)}
          <Text style={[styles.statusText, { color: getStatusColor(delivery.status) }]}>
            {delivery.status.replace('_', ' ').toUpperCase()}
          </Text>
        </View>
        <Text style={styles.orderAmount}>
          {formatCurrency(delivery.order.finalAmount)}
        </Text>
      </View>

      <View style={styles.addressContainer}>
        <View style={styles.addressRow}>
          <View style={styles.addressIcon}>
            <MapPin size={12} color={colors.warning} />
          </View>
          <View style={styles.addressInfo}>
            <Text style={styles.addressLabel}>From</Text>
            <Text style={styles.addressText} numberOfLines={1}>
              {delivery.pickupAddress}
            </Text>
          </View>
        </View>

        <View style={styles.addressRow}>
          <View style={styles.addressIcon}>
            <MapPin size={12} color={colors.success} />
          </View>
          <View style={styles.addressInfo}>
            <Text style={styles.addressLabel}>To</Text>
            <Text style={styles.addressText} numberOfLines={1}>
              {delivery.deliveryAddress}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.deliveryFooter}>
        <View style={styles.driverInfo}>
          <Text style={styles.driverLabel}>Driver:</Text>
          <Text style={styles.driverName}>{delivery.driver.name}</Text>
        </View>
        <View style={styles.dateInfo}>
          <Text style={styles.dateText}>
            {delivery.deliveredAt
              ? formatDate(delivery.deliveredAt)
              : formatDate(delivery.createdAt)}
          </Text>
          <ChevronRight size={16} color={colors.textMuted} />
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Delivery History</Text>
          <View style={styles.placeholder} />
        </View>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Delivery History</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {deliveries.length === 0 ? (
          <View style={styles.emptyState}>
            <Package size={64} color={colors.textMuted} />
            <Text style={styles.emptyTitle}>No Deliveries</Text>
            <Text style={styles.emptySubtitle}>
              Your delivery history will appear here once you place orders
            </Text>
          </View>
        ) : (
          <View style={styles.deliveriesList}>
            {deliveries.map(renderDeliveryCard)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.xs,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  deliveriesList: {
    padding: spacing.lg,
  },
  deliveryCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    elevation: 2,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  deliveryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  addressContainer: {
    marginBottom: spacing.md,
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  addressIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
    marginTop: 2,
  },
  addressInfo: {
    flex: 1,
  },
  addressLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: colors.textMuted,
    textTransform: 'uppercase',
    marginBottom: spacing.xs,
  },
  addressText: {
    fontSize: 14,
    color: colors.text,
  },
  deliveryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.sm,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverLabel: {
    fontSize: 12,
    color: colors.textMuted,
    marginRight: spacing.xs,
  },
  driverName: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.text,
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: colors.textMuted,
    marginRight: spacing.xs,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
    paddingHorizontal: spacing.lg,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.textMuted,
    textAlign: 'center',
  },
});
