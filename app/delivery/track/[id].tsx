import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-maps';
import * as Location from 'expo-location';
import {
  ArrowLeft,
  MapPin,
  Clock,
  User,
  Phone,
  Navigation,
  Package,
  CheckCircle,
  AlertCircle,
} from 'lucide-react-native';
import { colors, spacing, borderRadius } from '../../../constants/colors';
import { apiService } from '../../../services/api';
import { LoadingSpinner } from '../../../components/LoadingSpinner';

const { width, height } = Dimensions.get('window');

interface DeliveryTracking {
  delivery: {
    id: string;
    status: string;
    pickupLatitude: number;
    pickupLongitude: number;
    pickupAddress: string;
    deliveryLatitude: number;
    deliveryLongitude: number;
    deliveryAddress: string;
    currentLatitude?: number;
    currentLongitude?: number;
    estimatedDeliveryTime: string;
    driver: {
      id: string;
      name: string;
      phone: string;
    };
    order: {
      id: string;
      finalAmount: number;
    };
  };
  estimatedTimeRemaining: number;
  distanceRemaining: number;
}

export default function DeliveryTrackingScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const mapRef = useRef<MapView>(null);
  const [tracking, setTracking] = useState<DeliveryTracking | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);

  useEffect(() => {
    loadDeliveryTracking();
    requestLocationPermission();
    
    // Set up real-time tracking (poll every 30 seconds)
    const interval = setInterval(loadDeliveryTracking, 30000);
    return () => clearInterval(interval);
  }, [id]);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({});
        setUserLocation(location);
      }
    } catch (error) {
      console.error('Error getting location permission:', error);
    }
  };

  const loadDeliveryTracking = async () => {
    try {
      const response = await apiService.getDeliveryTracking(id as string);
      if (response.success && response.data) {
        setTracking(response.data);
        
        // Fit map to show all relevant points
        if (mapRef.current && response.data.delivery) {
          const { delivery } = response.data;
          const coordinates = [
            { latitude: delivery.pickupLatitude, longitude: delivery.pickupLongitude },
            { latitude: delivery.deliveryLatitude, longitude: delivery.deliveryLongitude },
          ];
          
          if (delivery.currentLatitude && delivery.currentLongitude) {
            coordinates.push({
              latitude: delivery.currentLatitude,
              longitude: delivery.currentLongitude,
            });
          }
          
          mapRef.current.fitToCoordinates(coordinates, {
            edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
            animated: true,
          });
        }
      } else {
        Alert.alert('Error', 'Failed to load delivery tracking');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load delivery tracking');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDeliveryTracking();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned':
        return colors.warning;
      case 'picked_up':
        return colors.primary;
      case 'in_transit':
        return colors.primary;
      case 'delivered':
        return colors.success;
      case 'failed':
        return colors.error;
      default:
        return colors.textMuted;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Clock size={16} color={colors.warning} />;
      case 'picked_up':
        return <Package size={16} color={colors.primary} />;
      case 'in_transit':
        return <Navigation size={16} color={colors.primary} />;
      case 'delivered':
        return <CheckCircle size={16} color={colors.success} />;
      case 'failed':
        return <AlertCircle size={16} color={colors.error} />;
      default:
        return <Clock size={16} color={colors.textMuted} />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
    }).format(amount);
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Track Delivery</Text>
          <View style={styles.placeholder} />
        </View>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  if (!tracking) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Track Delivery</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.errorState}>
          <AlertCircle size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Delivery Not Found</Text>
          <Text style={styles.errorSubtitle}>
            Unable to load delivery tracking information
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const { delivery } = tracking;

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Track Delivery</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
          disabled={refreshing}
        >
          <Navigation size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Map */}
        <View style={styles.mapContainer}>
          <MapView
            ref={mapRef}
            style={styles.map}
            initialRegion={{
              latitude: delivery.pickupLatitude,
              longitude: delivery.pickupLongitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
          >
            {/* Pickup Location */}
            <Marker
              coordinate={{
                latitude: delivery.pickupLatitude,
                longitude: delivery.pickupLongitude,
              }}
              title="Pickup Location"
              description={delivery.pickupAddress}
              pinColor={colors.warning}
            />

            {/* Delivery Location */}
            <Marker
              coordinate={{
                latitude: delivery.deliveryLatitude,
                longitude: delivery.deliveryLongitude,
              }}
              title="Delivery Location"
              description={delivery.deliveryAddress}
              pinColor={colors.success}
            />

            {/* Current Driver Location */}
            {delivery.currentLatitude && delivery.currentLongitude && (
              <Marker
                coordinate={{
                  latitude: delivery.currentLatitude,
                  longitude: delivery.currentLongitude,
                }}
                title="Driver Location"
                description="Current driver position"
                pinColor={colors.primary}
              />
            )}

            {/* Route Line */}
            {delivery.currentLatitude && delivery.currentLongitude && (
              <Polyline
                coordinates={[
                  {
                    latitude: delivery.currentLatitude,
                    longitude: delivery.currentLongitude,
                  },
                  {
                    latitude: delivery.deliveryLatitude,
                    longitude: delivery.deliveryLongitude,
                  },
                ]}
                strokeColor={colors.primary}
                strokeWidth={3}
                lineDashPattern={[5, 5]}
              />
            )}
          </MapView>
        </View>

        {/* Status Card */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <View style={styles.statusIndicator}>
              {getStatusIcon(delivery.status)}
              <Text style={[styles.statusText, { color: getStatusColor(delivery.status) }]}>
                {delivery.status.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
            <Text style={styles.orderAmount}>
              {formatCurrency(delivery.order.finalAmount)}
            </Text>
          </View>
          
          {tracking.estimatedTimeRemaining > 0 && (
            <View style={styles.estimateRow}>
              <Clock size={16} color={colors.textMuted} />
              <Text style={styles.estimateText}>
                Estimated arrival: {formatTime(tracking.estimatedTimeRemaining)}
              </Text>
            </View>
          )}
          
          <View style={styles.estimateRow}>
            <MapPin size={16} color={colors.textMuted} />
            <Text style={styles.estimateText}>
              Distance remaining: {tracking.distanceRemaining.toFixed(1)} km
            </Text>
          </View>
        </View>

        {/* Driver Info */}
        <View style={styles.driverCard}>
          <Text style={styles.cardTitle}>Driver Information</Text>
          <View style={styles.driverInfo}>
            <View style={styles.driverAvatar}>
              <User size={20} color={colors.white} />
            </View>
            <View style={styles.driverDetails}>
              <Text style={styles.driverName}>{delivery.driver.name}</Text>
              <Text style={styles.driverPhone}>{delivery.driver.phone}</Text>
            </View>
            <TouchableOpacity
              style={styles.callButton}
              onPress={() => Alert.alert('Info', 'Call functionality will be implemented')}
            >
              <Phone size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Addresses */}
        <View style={styles.addressCard}>
          <Text style={styles.cardTitle}>Delivery Details</Text>
          
          <View style={styles.addressItem}>
            <View style={styles.addressIcon}>
              <MapPin size={16} color={colors.warning} />
            </View>
            <View style={styles.addressInfo}>
              <Text style={styles.addressLabel}>Pickup</Text>
              <Text style={styles.addressText}>{delivery.pickupAddress}</Text>
            </View>
          </View>
          
          <View style={styles.addressItem}>
            <View style={styles.addressIcon}>
              <MapPin size={16} color={colors.success} />
            </View>
            <View style={styles.addressInfo}>
              <Text style={styles.addressLabel}>Delivery</Text>
              <Text style={styles.addressText}>{delivery.deliveryAddress}</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.xs,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  refreshButton: {
    padding: spacing.xs,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  mapContainer: {
    height: height * 0.4,
    backgroundColor: colors.border,
  },
  map: {
    flex: 1,
  },
  statusCard: {
    backgroundColor: colors.surface,
    margin: spacing.lg,
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  estimateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  estimateText: {
    fontSize: 14,
    color: colors.textMuted,
    marginLeft: spacing.sm,
  },
  driverCard: {
    backgroundColor: colors.surface,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.md,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  driverPhone: {
    fontSize: 14,
    color: colors.textMuted,
  },
  callButton: {
    padding: spacing.sm,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.primary + '20',
  },
  addressCard: {
    backgroundColor: colors.surface,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.xl,
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  addressItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  addressIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
    marginTop: spacing.xs,
  },
  addressInfo: {
    flex: 1,
  },
  addressLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textMuted,
    textTransform: 'uppercase',
    marginBottom: spacing.xs,
  },
  addressText: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  errorSubtitle: {
    fontSize: 14,
    color: colors.textMuted,
    textAlign: 'center',
  },
});
