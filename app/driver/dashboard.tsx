import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import {
  ArrowLeft,
  Navigation,
  Package,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  Phone,
  User,
} from 'lucide-react-native';
import { colors, spacing, borderRadius } from '../../constants/colors';
import { useAuth } from '../../context/AuthContext';
import { apiService } from '../../services/api';
import { locationService } from '../../services/locationService';
import { LoadingSpinner } from '../../components/LoadingSpinner';

interface Delivery {
  id: string;
  status: string;
  pickupAddress: string;
  deliveryAddress: string;
  pickupLatitude: number;
  pickupLongitude: number;
  deliveryLatitude: number;
  deliveryLongitude: number;
  estimatedDeliveryTime: string;
  order: {
    id: string;
    finalAmount: number;
    user: {
      name: string;
      phone: string;
    };
  };
}

export default function DriverDashboardScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [deliveries, setDeliveries] = useState<Delivery[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isOnline, setIsOnline] = useState(false);
  const [trackingLocation, setTrackingLocation] = useState(false);

  useEffect(() => {
    loadDeliveries();
  }, []);

  useEffect(() => {
    if (isOnline && trackingLocation) {
      startLocationTracking();
    } else {
      stopLocationTracking();
    }
  }, [isOnline, trackingLocation]);

  const loadDeliveries = async () => {
    try {
      // For demo purposes, we'll use mock data since we don't have driver-specific endpoints
      // In a real app, you'd call apiService.getDriverDeliveries(user.id)
      setDeliveries([]);
    } catch (error) {
      Alert.alert('Error', 'Failed to load deliveries');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDeliveries();
    setRefreshing(false);
  };

  const startLocationTracking = async () => {
    const success = await locationService.startLocationTracking(
      (location) => {
        // Update driver location for active deliveries
        console.log('Driver location updated:', location.coords);
        // In a real app, you'd update the server with the new location
      },
      (error) => {
        Alert.alert('Location Error', 'Failed to track location');
        setTrackingLocation(false);
      }
    );

    if (!success) {
      setTrackingLocation(false);
    }
  };

  const stopLocationTracking = async () => {
    await locationService.stopLocationTracking();
  };

  const handleStatusUpdate = (delivery: Delivery, newStatus: string) => {
    Alert.alert(
      'Update Status',
      `Change delivery status to ${newStatus.replace('_', ' ')}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: async () => {
            try {
              const response = await apiService.updateDeliveryStatus(
                delivery.id,
                newStatus,
                `Status updated by driver`
              );
              if (response.success) {
                Alert.alert('Success', 'Delivery status updated');
                onRefresh();
              } else {
                Alert.alert('Error', 'Failed to update status');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to update status');
            }
          },
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned':
        return colors.warning;
      case 'picked_up':
        return colors.primary;
      case 'in_transit':
        return colors.primary;
      case 'delivered':
        return colors.success;
      case 'failed':
        return colors.error;
      default:
        return colors.textMuted;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Clock size={16} color={colors.warning} />;
      case 'picked_up':
        return <Package size={16} color={colors.primary} />;
      case 'in_transit':
        return <Navigation size={16} color={colors.primary} />;
      case 'delivered':
        return <CheckCircle size={16} color={colors.success} />;
      case 'failed':
        return <AlertCircle size={16} color={colors.error} />;
      default:
        return <Clock size={16} color={colors.textMuted} />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
    }).format(amount);
  };

  const renderDeliveryCard = (delivery: Delivery) => (
    <View key={delivery.id} style={styles.deliveryCard}>
      <View style={styles.deliveryHeader}>
        <View style={styles.statusContainer}>
          {getStatusIcon(delivery.status)}
          <Text style={[styles.statusText, { color: getStatusColor(delivery.status) }]}>
            {delivery.status.replace('_', ' ').toUpperCase()}
          </Text>
        </View>
        <Text style={styles.orderAmount}>
          {formatCurrency(delivery.order.finalAmount)}
        </Text>
      </View>

      <View style={styles.customerInfo}>
        <User size={16} color={colors.textMuted} />
        <Text style={styles.customerName}>{delivery.order.user.name}</Text>
        <TouchableOpacity
          style={styles.phoneButton}
          onPress={() => Alert.alert('Info', 'Call functionality will be implemented')}
        >
          <Phone size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.addressContainer}>
        <View style={styles.addressRow}>
          <MapPin size={12} color={colors.warning} />
          <Text style={styles.addressText} numberOfLines={1}>
            Pickup: {delivery.pickupAddress}
          </Text>
        </View>
        <View style={styles.addressRow}>
          <MapPin size={12} color={colors.success} />
          <Text style={styles.addressText} numberOfLines={1}>
            Delivery: {delivery.deliveryAddress}
          </Text>
        </View>
      </View>

      <View style={styles.actionButtons}>
        {delivery.status === 'assigned' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={() => handleStatusUpdate(delivery, 'picked_up')}
          >
            <Text style={styles.actionButtonText}>Mark Picked Up</Text>
          </TouchableOpacity>
        )}
        {delivery.status === 'picked_up' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={() => handleStatusUpdate(delivery, 'in_transit')}
          >
            <Text style={styles.actionButtonText}>Start Delivery</Text>
          </TouchableOpacity>
        )}
        {delivery.status === 'in_transit' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.success }]}
            onPress={() => handleStatusUpdate(delivery, 'delivered')}
          >
            <Text style={styles.actionButtonText}>Mark Delivered</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Driver Dashboard</Text>
          <View style={styles.placeholder} />
        </View>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Driver Dashboard</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Status Controls */}
      <View style={styles.statusControls}>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Online Status</Text>
          <Switch
            value={isOnline}
            onValueChange={setIsOnline}
            trackColor={{ false: colors.border, true: colors.success + '40' }}
            thumbColor={isOnline ? colors.success : colors.textMuted}
          />
        </View>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Location Tracking</Text>
          <Switch
            value={trackingLocation}
            onValueChange={setTrackingLocation}
            disabled={!isOnline}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={trackingLocation ? colors.primary : colors.textMuted}
          />
        </View>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {deliveries.length === 0 ? (
          <View style={styles.emptyState}>
            <Package size={64} color={colors.textMuted} />
            <Text style={styles.emptyTitle}>No Active Deliveries</Text>
            <Text style={styles.emptySubtitle}>
              {isOnline
                ? 'You will receive delivery assignments here'
                : 'Go online to receive delivery assignments'}
            </Text>
          </View>
        ) : (
          <View style={styles.deliveriesList}>
            {deliveries.map(renderDeliveryCard)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.xs,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  placeholder: {
    width: 32,
  },
  statusControls: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statusLabel: {
    fontSize: 16,
    color: colors.text,
  },
  content: {
    flex: 1,
  },
  deliveriesList: {
    padding: spacing.lg,
  },
  deliveryCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  deliveryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  customerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  customerName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginLeft: spacing.sm,
    flex: 1,
  },
  phoneButton: {
    padding: spacing.sm,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.primary + '20',
  },
  addressContainer: {
    marginBottom: spacing.md,
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  addressText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: spacing.sm,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  actionButton: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    alignItems: 'center',
  },
  actionButtonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
    paddingHorizontal: spacing.lg,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.textMuted,
    textAlign: 'center',
  },
});
