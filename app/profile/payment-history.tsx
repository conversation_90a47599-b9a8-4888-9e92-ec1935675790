import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import {
  ArrowLeft,
  CreditCard,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  ChevronRight,
} from 'lucide-react-native';
import { colors, spacing, borderRadius } from '../../constants/colors';
import { useAuth } from '../../context/AuthContext';
import { apiService } from '../../services/api';
import { LoadingSpinner } from '../../components/LoadingSpinner';

interface Payment {
  id: string;
  amount: number;
  provider: 'mtn' | 'airtel';
  status: 'pending' | 'paid' | 'failed' | 'cancelled';
  referenceId: string;
  createdAt: string;
  orderId: string;
  orderAmount: number;
}

export default function PaymentHistoryScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadPaymentHistory();
  }, []);

  const loadPaymentHistory = async () => {
    try {
      const response = await apiService.getPaymentHistory();
      if (response.success && response.data) {
        setPayments(response.data);
      } else {
        Alert.alert('Error', 'Failed to load payment history');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load payment history');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPaymentHistory();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return colors.success;
      case 'pending':
        return colors.warning;
      case 'failed':
        return colors.error;
      case 'cancelled':
        return colors.textMuted;
      default:
        return colors.textMuted;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle size={16} color={colors.success} />;
      case 'pending':
        return <Clock size={16} color={colors.warning} />;
      case 'failed':
        return <XCircle size={16} color={colors.error} />;
      case 'cancelled':
        return <AlertCircle size={16} color={colors.textMuted} />;
      default:
        return <Clock size={16} color={colors.textMuted} />;
    }
  };

  const getProviderName = (provider: string) => {
    switch (provider) {
      case 'mtn':
        return 'MTN Mobile Money';
      case 'airtel':
        return 'Airtel Money';
      default:
        return provider.toUpperCase();
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-UG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderPaymentCard = (payment: Payment) => (
    <TouchableOpacity
      key={payment.id}
      style={styles.paymentCard}
      onPress={() => router.push(`/orders/${payment.orderId}`)}
    >
      <View style={styles.paymentHeader}>
        <View style={styles.paymentInfo}>
          <CreditCard size={20} color={colors.primary} />
          <View style={styles.paymentDetails}>
            <Text style={styles.providerName}>
              {getProviderName(payment.provider)}
            </Text>
            <Text style={styles.referenceId}>
              Ref: {payment.referenceId}
            </Text>
          </View>
        </View>
        
        <View style={styles.statusContainer}>
          {getStatusIcon(payment.status)}
          <Text style={[styles.statusText, { color: getStatusColor(payment.status) }]}>
            {payment.status.toUpperCase()}
          </Text>
        </View>
      </View>

      <View style={styles.paymentBody}>
        <View style={styles.amountContainer}>
          <Text style={styles.amountLabel}>Payment Amount</Text>
          <Text style={styles.amountValue}>
            {formatCurrency(payment.amount)}
          </Text>
        </View>

        <View style={styles.orderContainer}>
          <Text style={styles.orderLabel}>Order Total</Text>
          <Text style={styles.orderValue}>
            {formatCurrency(payment.orderAmount)}
          </Text>
        </View>
      </View>

      <View style={styles.paymentFooter}>
        <Text style={styles.dateText}>
          {formatDate(payment.createdAt)}
        </Text>
        <View style={styles.viewOrder}>
          <Text style={styles.viewOrderText}>View Order</Text>
          <ChevronRight size={16} color={colors.primary} />
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Payment History</Text>
          <View style={styles.placeholder} />
        </View>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Payment History</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {payments.length === 0 ? (
          <View style={styles.emptyState}>
            <CreditCard size={64} color={colors.textMuted} />
            <Text style={styles.emptyTitle}>No Payment History</Text>
            <Text style={styles.emptySubtitle}>
              Your payment history will appear here once you make purchases
            </Text>
          </View>
        ) : (
          <View style={styles.paymentsList}>
            {payments.map(renderPaymentCard)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.xs,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  paymentsList: {
    padding: spacing.lg,
  },
  paymentCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    elevation: 2,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  paymentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentDetails: {
    marginLeft: spacing.sm,
    flex: 1,
  },
  providerName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  referenceId: {
    fontSize: 12,
    color: colors.textMuted,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: spacing.xs,
  },
  paymentBody: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
    paddingVertical: spacing.sm,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.border,
  },
  amountContainer: {
    flex: 1,
  },
  amountLabel: {
    fontSize: 12,
    color: colors.textMuted,
    marginBottom: spacing.xs,
  },
  amountValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
  },
  orderContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  orderLabel: {
    fontSize: 12,
    color: colors.textMuted,
    marginBottom: spacing.xs,
  },
  orderValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
  },
  paymentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: colors.textMuted,
  },
  viewOrder: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewOrderText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
    marginRight: spacing.xs,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
    paddingHorizontal: spacing.lg,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.textMuted,
    textAlign: 'center',
  },
});
