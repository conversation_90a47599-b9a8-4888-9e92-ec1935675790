import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import {
  ArrowLeft,
  Bell,
  Shield,
  Globe,
  Moon,
  Smartphone,
  ChevronRight,
  Lock,
  CreditCard,
  MapPin,
  Truck,
  Star,
  HelpCircle,
  FileText,
  Users,
  Volume2,
  Mail,
} from 'lucide-react-native';
import { colors, spacing, borderRadius } from '../../constants/colors';

interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: React.ReactNode;
  type: 'toggle' | 'navigation' | 'action';
  value?: boolean;
  onPress?: () => void;
  onToggle?: (value: boolean) => void;
}

export default function SettingsScreen() {
  const router = useRouter();
  const [settings, setSettings] = useState({
    pushNotifications: true,
    emailNotifications: false,
    orderUpdates: true,
    promotions: false,
    deliveryUpdates: true,
    soundNotifications: true,
    darkMode: false,
    biometric: false,
    locationServices: true,
    autoSave: true,
  });

  const handleToggle = (key: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const settingsSections = [
    {
      title: 'Account',
      items: [
        {
          id: 'paymentHistory',
          title: 'Payment History',
          subtitle: 'View your payment transactions',
          icon: <CreditCard size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => router.push('/profile/payment-history'),
        },
        {
          id: 'addresses',
          title: 'Delivery Addresses',
          subtitle: 'Manage your saved addresses',
          icon: <MapPin size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => router.push('/profile/addresses'),
        },
        {
          id: 'deliveryHistory',
          title: 'Delivery History',
          subtitle: 'Track your past deliveries',
          icon: <Truck size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => router.push('/delivery/history'),
        },
      ],
    },
    {
      title: 'Notifications',
      items: [
        {
          id: 'push',
          title: 'Push Notifications',
          subtitle: 'Receive notifications on your device',
          icon: <Bell size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.pushNotifications,
          onToggle: () => handleToggle('pushNotifications'),
        },
        {
          id: 'email',
          title: 'Email Notifications',
          subtitle: 'Receive notifications via email',
          icon: <Mail size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.emailNotifications,
          onToggle: () => handleToggle('emailNotifications'),
        },
        {
          id: 'orders',
          title: 'Order Updates',
          subtitle: 'Get notified about order status changes',
          icon: <Bell size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.orderUpdates,
          onToggle: () => handleToggle('orderUpdates'),
        },
        {
          id: 'deliveryUpdates',
          title: 'Delivery Updates',
          subtitle: 'Real-time delivery tracking notifications',
          icon: <Truck size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.deliveryUpdates,
          onToggle: () => handleToggle('deliveryUpdates'),
        },
        {
          id: 'promotions',
          title: 'Promotions & Offers',
          subtitle: 'Receive promotional notifications',
          icon: <Star size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.promotions,
          onToggle: () => handleToggle('promotions'),
        },
        {
          id: 'soundNotifications',
          title: 'Sound Notifications',
          subtitle: 'Play sound for notifications',
          icon: <Volume2 size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.soundNotifications,
          onToggle: () => handleToggle('soundNotifications'),
        },
      ],
    },
    {
      title: 'Security',
      items: [
        {
          id: 'password',
          title: 'Change Password',
          subtitle: 'Update your account password',
          icon: <Lock size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => router.push('/profile/change-password'),
        },
        {
          id: 'biometric',
          title: 'Biometric Login',
          subtitle: 'Use fingerprint or face ID to login',
          icon: <Shield size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.biometric,
          onToggle: () => handleToggle('biometric'),
        },
      ],
    },
    {
      title: 'Preferences',
      items: [
        {
          id: 'language',
          title: 'Language',
          subtitle: 'English',
          icon: <Globe size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => Alert.alert('Info', 'Language selection will be implemented'),
        },
        {
          id: 'theme',
          title: 'Dark Mode',
          subtitle: 'Switch to dark theme',
          icon: <Moon size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.darkMode,
          onToggle: () => handleToggle('darkMode'),
        },
        {
          id: 'locationServices',
          title: 'Location Services',
          subtitle: 'Allow app to access your location',
          icon: <MapPin size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.locationServices,
          onToggle: () => handleToggle('locationServices'),
        },
        {
          id: 'autoSave',
          title: 'Auto-save Addresses',
          subtitle: 'Automatically save delivery addresses',
          icon: <MapPin size={20} color={colors.textMuted} />,
          type: 'toggle' as const,
          value: settings.autoSave,
          onToggle: () => handleToggle('autoSave'),
        },
      ],
    },
    {
      title: 'Support & Help',
      items: [
        {
          id: 'help',
          title: 'Help Center',
          subtitle: 'Get help and support',
          icon: <HelpCircle size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => Alert.alert('Info', 'Help center will be implemented'),
        },
        {
          id: 'contact',
          title: 'Contact Us',
          subtitle: 'Get in touch with our support team',
          icon: <Users size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => Alert.alert('Info', 'Contact support will be implemented'),
        },
        {
          id: 'feedback',
          title: 'Send Feedback',
          subtitle: 'Help us improve the app',
          icon: <Star size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => Alert.alert('Info', 'Feedback form will be implemented'),
        },
      ],
    },
    {
      title: 'Data & Privacy',
      items: [
        {
          id: 'privacy',
          title: 'Privacy Policy',
          icon: <Shield size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => Alert.alert('Info', 'Privacy policy will be implemented'),
        },
        {
          id: 'terms',
          title: 'Terms of Service',
          icon: <Shield size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => Alert.alert('Info', 'Terms of service will be implemented'),
        },
        {
          id: 'data',
          title: 'Data Usage',
          subtitle: 'Manage your data preferences',
          icon: <Shield size={20} color={colors.textMuted} />,
          type: 'navigation' as const,
          onPress: () => Alert.alert('Info', 'Data usage settings will be implemented'),
        },
      ],
    },
  ];

  const renderSettingItem = (item: SettingItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.settingItem}
      onPress={item.type === 'navigation' ? item.onPress : undefined}
      disabled={item.type === 'toggle'}
    >
      <View style={styles.settingLeft}>
        {item.icon}
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{item.title}</Text>
          {item.subtitle && (
            <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
          )}
        </View>
      </View>
      
      <View style={styles.settingRight}>
        {item.type === 'toggle' ? (
          <Switch
            value={item.value}
            onValueChange={item.onToggle}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={item.value ? colors.primary : colors.textMuted}
          />
        ) : (
          <ChevronRight size={20} color={colors.textMuted} />
        )}
      </View>
    </TouchableOpacity>
  );

  const renderSection = (section: typeof settingsSections[0]) => (
    <View key={section.title} style={styles.section}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      <View style={styles.sectionContent}>
        {section.items.map(renderSettingItem)}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {settingsSections.map(renderSection)}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.xs,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textMuted,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  sectionContent: {
    backgroundColor: colors.surface,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: spacing.md,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  settingSubtitle: {
    fontSize: 12,
    color: colors.textMuted,
  },
  settingRight: {
    marginLeft: spacing.md,
  },
});
