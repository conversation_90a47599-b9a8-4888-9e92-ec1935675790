# Tulia E-commerce Backend

A complete NestJS backend for the Tulia mobile e-commerce application with PostgreSQL database, JWT authentication, and mobile money payment integration.

## Features

- **Authentication & Authorization**: JWT-based auth with role-based access control
- **Product Management**: CRUD operations with categories and search
- **Order Management**: Complete order lifecycle with status tracking
- **Address Management**: User addresses with GPS coordinates
- **Delivery Zones**: GPS-based delivery fee calculation
- **Mobile Money Payments**: MTN and Airtel MoMo integration (mock implementation)
- **Admin Panel**: Protected admin endpoints for management
- **API Documentation**: Swagger/OpenAPI documentation

## Tech Stack

- **Framework**: NestJS
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT with Passport
- **Validation**: class-validator
- **Documentation**: Swagger/OpenAPI
- **Environment**: Node.js

## Quick Start

### Prerequisites

- Node.js (v16 or higher)
- PostgreSQL database
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

3. Create PostgreSQL database:
```sql
CREATE DATABASE tulia_db;
```

4. Run database migrations (if any):
```bash
npm run migration:run
```

5. Seed the database:
```bash
npm run seed
```

6. Start the development server:
```bash
npm run start:dev
```

The API will be available at `http://localhost:3000`
Swagger documentation at `http://localhost:3000/api/docs`

## Test Accounts

After seeding, you can use these test accounts:

- **Admin**: <EMAIL> / admin123
- **User**: <EMAIL> / user123

## API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - Login user
- `GET /auth/profile` - Get current user profile

### Products
- `GET /products` - Get all products (with optional category/search filters)
- `GET /products/categories` - Get all categories
- `GET /products/:id` - Get product by ID
- `POST /products` - Create product (Admin only)
- `PATCH /products/:id` - Update product (Admin only)
- `DELETE /products/:id` - Delete product (Admin only)

### Orders
- `POST /orders` - Create new order
- `GET /orders` - Get user orders (or all orders for admin)
- `GET /orders/:id` - Get order by ID
- `PATCH /orders/:id/status` - Update order status (Admin only)

### Addresses
- `POST /addresses` - Create address
- `GET /addresses` - Get user addresses
- `GET /addresses/:id` - Get address by ID
- `PATCH /addresses/:id` - Update address
- `DELETE /addresses/:id` - Delete address

### Delivery Zones
- `GET /delivery-zones` - Get all active zones
- `POST /delivery-zones/calculate` - Calculate delivery fee for coordinates
- `POST /delivery-zones` - Create zone (Admin only)
- `PATCH /delivery-zones/:id` - Update zone (Admin only)

### Payments
- `POST /payments/initiate` - Initiate mobile money payment
- `GET /payments/status/:referenceId` - Check payment status
- `GET /payments` - Get all payments (Admin only)

## Database Schema

### Users
- id, email, name, phone, password, role, timestamps

### Products
- id, name, description, price, category, imageUrl, stock, timestamps

### Orders
- id, userId, items (JSON), totalAmount, deliveryFee, finalAmount, status, paymentStatus, deliveryType, addressId, paymentMethod, paymentReference, timestamps

### Addresses
- id, userId, name, address, city, phone, latitude, longitude, isDefault, timestamps

### Delivery Zones
- id, name, fee, centerLatitude, centerLongitude, radius, isActive, timestamps

### Payments
- id, orderId, provider, phoneNumber, amount, referenceId, status, externalTransactionId, providerResponse, timestamps

## Environment Variables

```env
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=tulia_db

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# App
PORT=3000
NODE_ENV=development

# Mobile Money (Mock)
MTN_API_KEY=mock-mtn-api-key
AIRTEL_API_KEY=mock-airtel-api-key

# CORS
CORS_ORIGIN=http://localhost:8081,http://localhost:19006
```

## Development

### Available Scripts

- `npm run start` - Start production server
- `npm run start:dev` - Start development server with hot reload
- `npm run start:debug` - Start server in debug mode
- `npm run build` - Build for production
- `npm run seed` - Seed database with test data
- `npm run migration:generate` - Generate new migration
- `npm run migration:run` - Run pending migrations

### Project Structure

```
src/
├── auth/              # Authentication module
├── users/             # User management
├── products/          # Product management
├── orders/            # Order management
├── addresses/         # Address management
├── delivery-zones/    # Delivery zone management
├── payments/          # Payment processing
├── common/            # Shared utilities
├── config/            # Configuration files
└── database/          # Database seeds and migrations
```

## Mobile Money Integration

The current implementation includes mock services for MTN and Airtel MoMo. For production:

1. Register with MTN MoMo Developer Portal
2. Register with Airtel Money API
3. Replace mock implementations in `PaymentsService`
4. Update environment variables with real API keys

## Security Features

- JWT authentication with role-based access control
- Password hashing with bcrypt
- Input validation and sanitization
- CORS configuration
- Rate limiting (can be added)
- SQL injection prevention via TypeORM

## Deployment

1. Set up PostgreSQL database
2. Configure environment variables
3. Build the application: `npm run build`
4. Run migrations: `npm run migration:run`
5. Seed database: `npm run seed`
6. Start server: `npm run start:prod`

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## License

MIT License