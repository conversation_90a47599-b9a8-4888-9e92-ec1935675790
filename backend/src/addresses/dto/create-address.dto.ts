import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsBoolean, IsOptional } from 'class-validator';

export class CreateAddressDto {
  @ApiProperty({ example: 'Home' })
  @IsString()
  name: string;

  @ApiProperty({ example: '123 Main Street, Apartment 4B' })
  @IsString()
  address: string;

  @ApiProperty({ example: 'Kampala' })
  @IsString()
  city: string;

  @ApiProperty({ example: '+256700000000' })
  @IsString()
  phone: string;

  @ApiProperty({ example: 0.3476 })
  @IsNumber()
  latitude: number;

  @ApiProperty({ example: 32.5825 })
  @IsNumber()
  longitude: number;

  @ApiProperty({ example: false, required: false })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;
}