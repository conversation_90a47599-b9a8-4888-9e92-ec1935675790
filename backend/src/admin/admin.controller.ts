import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { AdminService } from './admin.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';

@ApiTags('Admin')
@ApiBearerAuth()
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('dashboard')
  @ApiOperation({ summary: 'Get admin dashboard statistics' })
  getDashboardStats() {
    return this.adminService.getDashboardStats();
  }

  @Get('analytics/overview')
  @ApiOperation({ summary: 'Get analytics overview' })
  @ApiQuery({ name: 'period', required: false, enum: ['7d', '30d', '90d', '1y'] })
  getAnalyticsOverview(@Query('period') period: string = '30d') {
    return this.adminService.getAnalyticsOverview(period);
  }

  @Get('analytics/sales')
  @ApiOperation({ summary: 'Get sales analytics' })
  @ApiQuery({ name: 'period', required: false, enum: ['7d', '30d', '90d', '1y'] })
  getSalesAnalytics(@Query('period') period: string = '30d') {
    return this.adminService.getSalesAnalytics(period);
  }

  @Get('analytics/users')
  @ApiOperation({ summary: 'Get user analytics' })
  @ApiQuery({ name: 'period', required: false, enum: ['7d', '30d', '90d', '1y'] })
  getUserAnalytics(@Query('period') period: string = '30d') {
    return this.adminService.getUserAnalytics(period);
  }

  @Get('analytics/products')
  @ApiOperation({ summary: 'Get product analytics' })
  @ApiQuery({ name: 'period', required: false, enum: ['7d', '30d', '90d', '1y'] })
  getProductAnalytics(@Query('period') period: string = '30d') {
    return this.adminService.getProductAnalytics(period);
  }

  @Get('users/search')
  @ApiOperation({ summary: 'Search users' })
  @ApiQuery({ name: 'q', required: false })
  @ApiQuery({ name: 'role', required: false, enum: ['user', 'admin'] })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  searchUsers(
    @Query('q') query?: string,
    @Query('role') role?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.adminService.searchUsers(query, role, page, limit);
  }

  @Patch('users/:id/status')
  @ApiOperation({ summary: 'Update user status (activate/deactivate)' })
  updateUserStatus(
    @Param('id') id: string,
    @Body() statusData: { isActive: boolean },
  ) {
    return this.adminService.updateUserStatus(id, statusData.isActive);
  }

  @Get('orders/search')
  @ApiOperation({ summary: 'Search orders' })
  @ApiQuery({ name: 'q', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  searchOrders(
    @Query('q') query?: string,
    @Query('status') status?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.adminService.searchOrders(query, status, page, limit);
  }

  @Get('products/search')
  @ApiOperation({ summary: 'Search products for admin' })
  @ApiQuery({ name: 'q', required: false })
  @ApiQuery({ name: 'category', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  searchProducts(
    @Query('q') query?: string,
    @Query('category') category?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.adminService.searchProducts(query, category, page, limit);
  }

  @Get('system/health')
  @ApiOperation({ summary: 'Get system health status' })
  getSystemHealth() {
    return this.adminService.getSystemHealth();
  }

  @Get('system/logs')
  @ApiOperation({ summary: 'Get system logs' })
  @ApiQuery({ name: 'level', required: false, enum: ['error', 'warn', 'info', 'debug'] })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  getSystemLogs(
    @Query('level') level?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 50,
  ) {
    return this.adminService.getSystemLogs(level, page, limit);
  }

  @Post('system/backup')
  @ApiOperation({ summary: 'Create system backup' })
  createBackup() {
    return this.adminService.createBackup();
  }

  @Get('reports/export')
  @ApiOperation({ summary: 'Export reports' })
  @ApiQuery({ name: 'type', required: true, enum: ['users', 'orders', 'products', 'sales'] })
  @ApiQuery({ name: 'format', required: false, enum: ['csv', 'xlsx', 'pdf'] })
  @ApiQuery({ name: 'period', required: false, enum: ['7d', '30d', '90d', '1y'] })
  exportReports(
    @Query('type') type: string,
    @Query('format') format: string = 'csv',
    @Query('period') period: string = '30d',
  ) {
    return this.adminService.exportReports(type, format, period);
  }
}
