import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, Like } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Order } from '../orders/entities/order.entity';
import { Product } from '../products/entities/product.entity';
import { Payment } from '../payments/entities/payment.entity';

@Injectable()
export class AdminService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Order)
    private ordersRepository: Repository<Order>,
    @InjectRepository(Product)
    private productsRepository: Repository<Product>,
    @InjectRepository(Payment)
    private paymentsRepository: Repository<Payment>,
  ) {}

  async getDashboardStats() {
    const [
      totalUsers,
      totalOrders,
      totalProducts,
      totalRevenue,
      recentOrders,
      topProducts,
    ] = await Promise.all([
      this.usersRepository.count(),
      this.ordersRepository.count(),
      this.productsRepository.count(),
      this.ordersRepository
        .createQueryBuilder('order')
        .select('SUM(order.finalAmount)', 'total')
        .where('order.status = :status', { status: 'delivered' })
        .getRawOne(),
      this.ordersRepository.find({
        take: 5,
        order: { createdAt: 'DESC' },
        relations: ['user'],
      }),
      this.getTopProducts(5),
    ]);

    return {
      totalUsers,
      totalOrders,
      totalProducts,
      totalRevenue: parseFloat(totalRevenue?.total || '0'),
      recentOrders,
      topProducts,
    };
  }

  async getAnalyticsOverview(period: string) {
    const dateRange = this.getDateRange(period);
    
    const [
      ordersCount,
      revenue,
      newUsers,
      averageOrderValue,
    ] = await Promise.all([
      this.ordersRepository.count({
        where: { createdAt: Between(dateRange.start, dateRange.end) },
      }),
      this.ordersRepository
        .createQueryBuilder('order')
        .select('SUM(order.finalAmount)', 'total')
        .where('order.createdAt BETWEEN :start AND :end', dateRange)
        .andWhere('order.status = :status', { status: 'delivered' })
        .getRawOne(),
      this.usersRepository.count({
        where: { createdAt: Between(dateRange.start, dateRange.end) },
      }),
      this.ordersRepository
        .createQueryBuilder('order')
        .select('AVG(order.finalAmount)', 'average')
        .where('order.createdAt BETWEEN :start AND :end', dateRange)
        .getRawOne(),
    ]);

    return {
      period,
      ordersCount,
      revenue: parseFloat(revenue?.total || '0'),
      newUsers,
      averageOrderValue: parseFloat(averageOrderValue?.average || '0'),
    };
  }

  async getSalesAnalytics(period: string) {
    const dateRange = this.getDateRange(period);
    
    const salesData = await this.ordersRepository
      .createQueryBuilder('order')
      .select([
        'DATE(order.createdAt) as date',
        'COUNT(*) as orders',
        'SUM(order.finalAmount) as revenue',
      ])
      .where('order.createdAt BETWEEN :start AND :end', dateRange)
      .groupBy('DATE(order.createdAt)')
      .orderBy('date', 'ASC')
      .getRawMany();

    return {
      period,
      salesData: salesData.map(item => ({
        date: item.date,
        orders: parseInt(item.orders),
        revenue: parseFloat(item.revenue),
      })),
    };
  }

  async getUserAnalytics(period: string) {
    const dateRange = this.getDateRange(period);
    
    const [
      newUsers,
      activeUsers,
      userGrowth,
    ] = await Promise.all([
      this.usersRepository
        .createQueryBuilder('user')
        .select([
          'DATE(user.createdAt) as date',
          'COUNT(*) as count',
        ])
        .where('user.createdAt BETWEEN :start AND :end', dateRange)
        .groupBy('DATE(user.createdAt)')
        .orderBy('date', 'ASC')
        .getRawMany(),
      this.usersRepository
        .createQueryBuilder('user')
        .innerJoin('user.orders', 'order')
        .where('order.createdAt BETWEEN :start AND :end', dateRange)
        .getCount(),
      this.usersRepository.count({
        where: { createdAt: Between(dateRange.start, dateRange.end) },
      }),
    ]);

    return {
      period,
      newUsers: newUsers.map(item => ({
        date: item.date,
        count: parseInt(item.count),
      })),
      activeUsers,
      userGrowth,
    };
  }

  async getProductAnalytics(period: string) {
    const dateRange = this.getDateRange(period);
    
    const topProducts = await this.getTopProducts(10, dateRange);
    const categoryStats = await this.getCategoryStats(dateRange);

    return {
      period,
      topProducts,
      categoryStats,
    };
  }

  async searchUsers(query?: string, role?: string, page: number = 1, limit: number = 10) {
    const queryBuilder = this.usersRepository.createQueryBuilder('user');
    
    if (query) {
      queryBuilder.where(
        'user.name ILIKE :query OR user.email ILIKE :query OR user.phone ILIKE :query',
        { query: `%${query}%` }
      );
    }
    
    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    const [users, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy('user.createdAt', 'DESC')
      .getManyAndCount();

    return {
      users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updateUserStatus(id: string, isActive: boolean) {
    // Note: This would require adding an isActive field to the User entity
    // For now, we'll return a placeholder response
    return {
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      userId: id,
      isActive,
    };
  }

  async searchOrders(query?: string, status?: string, page: number = 1, limit: number = 10) {
    const queryBuilder = this.ordersRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.user', 'user');
    
    if (query) {
      queryBuilder.where(
        'order.id ILIKE :query OR user.name ILIKE :query OR user.email ILIKE :query',
        { query: `%${query}%` }
      );
    }
    
    if (status) {
      queryBuilder.andWhere('order.status = :status', { status });
    }

    const [orders, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy('order.createdAt', 'DESC')
      .getManyAndCount();

    return {
      orders,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async searchProducts(query?: string, category?: string, page: number = 1, limit: number = 10) {
    const queryBuilder = this.productsRepository.createQueryBuilder('product');
    
    if (query) {
      queryBuilder.where(
        'product.name ILIKE :query OR product.description ILIKE :query',
        { query: `%${query}%` }
      );
    }
    
    if (category) {
      queryBuilder.andWhere('product.category = :category', { category });
    }

    const [products, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy('product.createdAt', 'DESC')
      .getManyAndCount();

    return {
      products,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getSystemHealth() {
    const [
      dbStatus,
      memoryUsage,
      uptime,
    ] = await Promise.all([
      this.checkDatabaseHealth(),
      this.getMemoryUsage(),
      process.uptime(),
    ]);

    return {
      status: 'healthy',
      database: dbStatus,
      memory: memoryUsage,
      uptime: Math.floor(uptime),
      timestamp: new Date(),
    };
  }

  async getSystemLogs(level?: string, page: number = 1, limit: number = 50) {
    // This is a placeholder - in a real app, you'd integrate with your logging system
    return {
      logs: [
        {
          id: '1',
          level: 'info',
          message: 'User logged in successfully',
          timestamp: new Date(),
          metadata: { userId: 'user-123' },
        },
        {
          id: '2',
          level: 'error',
          message: 'Payment processing failed',
          timestamp: new Date(),
          metadata: { orderId: 'order-456' },
        },
      ],
      total: 2,
      page,
      limit,
    };
  }

  async createBackup() {
    // This is a placeholder - in a real app, you'd implement actual backup logic
    return {
      message: 'Backup created successfully',
      backupId: `backup_${Date.now()}`,
      timestamp: new Date(),
    };
  }

  async exportReports(type: string, format: string, period: string) {
    // This is a placeholder - in a real app, you'd generate actual reports
    return {
      message: `${type} report exported successfully`,
      format,
      period,
      downloadUrl: `/admin/downloads/report_${type}_${Date.now()}.${format}`,
    };
  }

  private getDateRange(period: string) {
    const end = new Date();
    const start = new Date();
    
    switch (period) {
      case '7d':
        start.setDate(end.getDate() - 7);
        break;
      case '30d':
        start.setDate(end.getDate() - 30);
        break;
      case '90d':
        start.setDate(end.getDate() - 90);
        break;
      case '1y':
        start.setFullYear(end.getFullYear() - 1);
        break;
      default:
        start.setDate(end.getDate() - 30);
    }
    
    return { start, end };
  }

  private async getTopProducts(limit: number, dateRange?: { start: Date; end: Date }) {
    // For now, return mock data since the order items structure needs to be clarified
    return [
      {
        id: '1',
        name: 'Sample Product 1',
        imageUrl: 'https://example.com/product1.jpg',
        orderCount: 10,
        totalSold: 25,
      },
      {
        id: '2',
        name: 'Sample Product 2',
        imageUrl: 'https://example.com/product2.jpg',
        orderCount: 8,
        totalSold: 20,
      },
    ];
  }

  private async getCategoryStats(dateRange: { start: Date; end: Date }) {
    // For now, return mock data since the order items structure needs to be clarified
    return [
      {
        category: 'Electronics',
        orderCount: 15,
        revenue: 5000,
      },
      {
        category: 'Clothing',
        orderCount: 12,
        revenue: 3000,
      },
      {
        category: 'Books',
        orderCount: 8,
        revenue: 800,
      },
    ];
  }

  private async checkDatabaseHealth() {
    try {
      await this.usersRepository.query('SELECT 1');
      return { status: 'connected', latency: 0 };
    } catch (error) {
      return { status: 'disconnected', error: error.message };
    }
  }

  private getMemoryUsage() {
    const usage = process.memoryUsage();
    return {
      rss: Math.round(usage.rss / 1024 / 1024),
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024),
    };
  }
}
