import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../app.module';
import { SeederService } from './seeder.service';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const seederService = app.get(SeederService);

  try {
    const command = process.argv[2];

    switch (command) {
      case 'seed':
        await seederService.seedAll();
        break;
      case 'clear':
        await seederService.clearAll();
        break;
      case 'reset':
        await seederService.clearAll();
        await seederService.seedAll();
        break;
      default:
        console.log('Available commands:');
        console.log('  npm run seed        - Seed all data');
        console.log('  npm run seed:clear  - Clear all data');
        console.log('  npm run seed:reset  - Clear and reseed all data');
        break;
    }
  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

bootstrap();
