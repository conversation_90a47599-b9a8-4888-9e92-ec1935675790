import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User } from '../../users/entities/user.entity';
import { Product } from '../../products/entities/product.entity';
import { Order } from '../../orders/entities/order.entity';
import { Address } from '../../addresses/entities/address.entity';
import { DeliveryZone } from '../../delivery-zones/entities/delivery-zone.entity';
import { Delivery } from '../../delivery/entities/delivery.entity';
import { UserRole } from '../../common/enums/user-role.enum';
import { OrderStatus } from '../../common/enums/order-status.enum';
import { DeliveryStatus } from '../../delivery/entities/delivery.entity';

@Injectable()
export class SeederService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Product)
    private productsRepository: Repository<Product>,
    @InjectRepository(Order)
    private ordersRepository: Repository<Order>,
    @InjectRepository(Address)
    private addressesRepository: Repository<Address>,
    @InjectRepository(DeliveryZone)
    private deliveryZonesRepository: Repository<DeliveryZone>,
    @InjectRepository(Delivery)
    private deliveriesRepository: Repository<Delivery>,
  ) {}

  async seedAll() {
    console.log('🌱 Starting database seeding...');
    
    await this.seedUsers();
    await this.seedDeliveryZones();
    await this.seedProducts();
    await this.seedAddresses();
    await this.seedOrders();
    await this.seedDeliveries();
    
    console.log('✅ Database seeding completed!');
  }

  async seedUsers() {
    console.log('👥 Seeding users...');
    
    const users = [
      // Admin Users
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('admin123', 10),
        name: 'Admin User',
        phone: '+256700000001',
        role: UserRole.ADMIN,
        avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('super123', 10),
        name: 'Super Admin',
        phone: '+256700000002',
        role: UserRole.ADMIN,
        avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      },
      
      // Regular Users
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('user123', 10),
        name: 'John Doe',
        phone: '+256700000003',
        role: UserRole.USER,
        avatarUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('user123', 10),
        name: 'Jane Smith',
        phone: '+256700000004',
        role: UserRole.USER,
        avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('user123', 10),
        name: 'Mike Johnson',
        phone: '+256700000005',
        role: UserRole.USER,
        avatarUrl: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('user123', 10),
        name: 'Sarah Wilson',
        phone: '+256700000006',
        role: UserRole.USER,
        avatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      },
      
      // Driver Users (for delivery testing)
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('driver123', 10),
        name: 'David Driver',
        phone: '+256700000007',
        role: UserRole.USER, // In a real app, you might have a DRIVER role
        avatarUrl: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('driver123', 10),
        name: 'Lisa Delivery',
        phone: '+256700000008',
        role: UserRole.USER,
        avatarUrl: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face',
      },
    ];

    for (const userData of users) {
      const existingUser = await this.usersRepository.findOne({
        where: { email: userData.email },
      });

      if (!existingUser) {
        const user = this.usersRepository.create(userData);
        await this.usersRepository.save(user);
        console.log(`✓ Created user: ${userData.name} (${userData.email})`);
      } else {
        console.log(`- User already exists: ${userData.email}`);
      }
    }
  }

  async seedDeliveryZones() {
    console.log('🗺️ Seeding delivery zones...');
    
    const zones = [
      {
        name: 'Kampala Central',
        fee: 5000,
        centerLatitude: 0.3136,
        centerLongitude: 32.5811,
        radius: 5000, // 5km radius
        isActive: true,
      },
      {
        name: 'Nakawa',
        fee: 7000,
        centerLatitude: 0.3300,
        centerLongitude: 32.6100,
        radius: 7000, // 7km radius
        isActive: true,
      },
      {
        name: 'Kawempe',
        fee: 8000,
        centerLatitude: 0.3500,
        centerLongitude: 32.5500,
        radius: 6000, // 6km radius
        isActive: true,
      },
    ];

    for (const zoneData of zones) {
      const existingZone = await this.deliveryZonesRepository.findOne({
        where: { name: zoneData.name },
      });

      if (!existingZone) {
        const zone = this.deliveryZonesRepository.create(zoneData);
        await this.deliveryZonesRepository.save(zone);
        console.log(`✓ Created delivery zone: ${zoneData.name}`);
      } else {
        console.log(`- Delivery zone already exists: ${zoneData.name}`);
      }
    }
  }

  async seedProducts() {
    console.log('🍕 Seeding products...');
    
    const products = [
      // Pizza
      {
        name: 'Margherita Pizza',
        description: 'Classic pizza with tomato sauce, mozzarella cheese, and fresh basil',
        price: 25000,
        category: 'Pizza',
        imageUrl: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=400&h=300&fit=crop',
        stock: 50,
        isAvailable: true,
      },
      {
        name: 'Pepperoni Pizza',
        description: 'Delicious pizza topped with pepperoni and mozzarella cheese',
        price: 30000,
        category: 'Pizza',
        imageUrl: 'https://images.unsplash.com/photo-1628840042765-356cda07504e?w=400&h=300&fit=crop',
        stock: 45,
        isAvailable: true,
      },
      
      // Burgers
      {
        name: 'Classic Beef Burger',
        description: 'Juicy beef patty with lettuce, tomato, onion, and special sauce',
        price: 18000,
        category: 'Burgers',
        imageUrl: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=300&fit=crop',
        stock: 30,
        isAvailable: true,
      },
      {
        name: 'Chicken Burger',
        description: 'Grilled chicken breast with fresh vegetables and mayo',
        price: 16000,
        category: 'Burgers',
        imageUrl: 'https://images.unsplash.com/photo-1606755962773-d324e2d53352?w=400&h=300&fit=crop',
        stock: 25,
        isAvailable: true,
      },
      
      // Local Dishes
      {
        name: 'Matooke with Beef',
        description: 'Traditional steamed green bananas served with tender beef stew',
        price: 12000,
        category: 'Local Dishes',
        imageUrl: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=300&fit=crop',
        stock: 40,
        isAvailable: true,
      },
      {
        name: 'Posho and Beans',
        description: 'Traditional cornmeal served with seasoned beans',
        price: 8000,
        category: 'Local Dishes',
        imageUrl: 'https://images.unsplash.com/photo-1574484284002-952d92456975?w=400&h=300&fit=crop',
        stock: 35,
        isAvailable: true,
      },
      
      // Drinks
      {
        name: 'Fresh Orange Juice',
        description: 'Freshly squeezed orange juice',
        price: 5000,
        category: 'Beverages',
        imageUrl: 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=400&h=300&fit=crop',
        stock: 60,
        isAvailable: true,
      },
      {
        name: 'Coca Cola',
        description: 'Classic Coca Cola soft drink',
        price: 3000,
        category: 'Beverages',
        imageUrl: 'https://images.unsplash.com/photo-1629203851122-3726ecdf080e?w=400&h=300&fit=crop',
        stock: 100,
        isAvailable: true,
      },
    ];

    for (const productData of products) {
      const existingProduct = await this.productsRepository.findOne({
        where: { name: productData.name },
      });

      if (!existingProduct) {
        const product = this.productsRepository.create(productData);
        await this.productsRepository.save(product);
        console.log(`✓ Created product: ${productData.name}`);
      } else {
        console.log(`- Product already exists: ${productData.name}`);
      }
    }
  }

  async seedAddresses() {
    console.log('📍 Seeding addresses...');
    
    const users = await this.usersRepository.find({
      where: { role: UserRole.USER },
    });

    const addressTemplates = [
      {
        name: 'Home',
        address: 'Plot 123, Kampala Road',
        city: 'Kampala',
        phone: '+256700000001',
        latitude: 0.3476,
        longitude: 32.5825,
        isDefault: true,
      },
      {
        name: 'Office',
        address: 'Plot 456, Jinja Road',
        city: 'Kampala',
        phone: '+256700000002',
        latitude: 0.3136,
        longitude: 32.5811,
        isDefault: false,
      },
    ];

    for (const user of users.slice(0, 3)) { // Add addresses for first 3 users
      for (const [index, template] of addressTemplates.entries()) {
        const addressData = {
          ...template,
          userId: user.id,
          phone: user.phone,
          isDefault: index === 0, // First address is default
        };

        const existingAddress = await this.addressesRepository.findOne({
          where: { userId: user.id, name: template.name },
        });

        if (!existingAddress) {
          const address = this.addressesRepository.create(addressData);
          await this.addressesRepository.save(address);
          console.log(`✓ Created address for ${user.name}: ${template.name}`);
        }
      }
    }
  }

  async seedOrders() {
    console.log('🛒 Seeding orders...');

    const users = await this.usersRepository.find({
      where: { role: UserRole.USER },
    });
    const products = await this.productsRepository.find();
    const addresses = await this.addressesRepository.find();

    if (users.length === 0 || products.length === 0) {
      console.log('- Skipping orders: No users or products found');
      return;
    }

    const orderStatuses = [OrderStatus.PENDING, OrderStatus.PAID, OrderStatus.PROCESSING, OrderStatus.DELIVERED];

    for (let i = 0; i < 5; i++) {
      const user = users[i % users.length];
      const userAddress = addresses.find(addr => addr.userId === user.id);
      const randomProducts = products.slice(0, Math.floor(Math.random() * 3) + 1);

      const totalAmount = randomProducts.reduce((sum, product) => sum + product.price, 0);
      const deliveryFee = 5000;
      const finalAmount = totalAmount + deliveryFee;

      const orderData = {
        userId: user.id,
        items: randomProducts.map(product => ({
          productId: product.id,
          product: {
            id: product.id,
            name: product.name,
            price: product.price,
            imageUrl: product.imageUrl,
            category: product.category,
          },
          quantity: 1,
          price: product.price,
        })),
        totalAmount,
        deliveryFee,
        finalAmount,
        status: orderStatuses[i % orderStatuses.length],
        deliveryType: 'delivery' as const,
        addressId: userAddress?.id || null,
        paymentMethod: 'mtn' as const,
      };

      const order = this.ordersRepository.create(orderData);
      await this.ordersRepository.save(order);
      console.log(`✓ Created order for ${user.name}: ${finalAmount} UGX`);
    }
  }

  async seedDeliveries() {
    console.log('🚚 Seeding deliveries...');

    const orders = await this.ordersRepository.find({
      relations: ['user', 'address'],
      where: { status: OrderStatus.PAID },
    });
    const drivers = await this.usersRepository.find({
      where: { email: '<EMAIL>' },
    });

    if (orders.length === 0 || drivers.length === 0) {
      console.log('- Skipping deliveries: No confirmed orders or drivers found');
      return;
    }

    const driver = drivers[0];
    const deliveryStatuses = [DeliveryStatus.ASSIGNED, DeliveryStatus.PICKED_UP, DeliveryStatus.IN_TRANSIT];

    for (const [index, order] of orders.slice(0, 3).entries()) {
      const address = order.address;
      if (!address) {
        console.log(`- Skipping delivery for order ${order.id}: No address found`);
        continue;
      }

      const deliveryData = {
        orderId: order.id,
        driverId: driver.id,
        status: deliveryStatuses[index % deliveryStatuses.length],
        pickupLatitude: 0.3200,
        pickupLongitude: 32.5850,
        pickupAddress: 'Tulia Restaurant, Kampala Road',
        deliveryLatitude: address.latitude,
        deliveryLongitude: address.longitude,
        deliveryAddress: address.address,
        currentLatitude: 0.3300 + (index * 0.01),
        currentLongitude: 32.5800 + (index * 0.01),
        estimatedDeliveryTime: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
      };

      const delivery = this.deliveriesRepository.create(deliveryData);
      await this.deliveriesRepository.save(delivery);
      console.log(`✓ Created delivery for order ${order.id}`);
    }
  }

  async clearAll() {
    console.log('🗑️ Clearing all data...');
    
    await this.deliveriesRepository.delete({});
    await this.ordersRepository.delete({});
    await this.addressesRepository.delete({});
    await this.productsRepository.delete({});
    await this.deliveryZonesRepository.delete({});
    await this.usersRepository.delete({});
    
    console.log('✅ All data cleared!');
  }
}
