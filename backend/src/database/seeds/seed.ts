import { DataSource } from 'typeorm';
import { dataSourceOptions } from '../../config/database.config';
import { User } from '../../users/entities/user.entity';
import { Product } from '../../products/entities/product.entity';
import { DeliveryZone } from '../../delivery-zones/entities/delivery-zone.entity';
import { UserRole } from '../../common/enums/user-role.enum';
import * as bcrypt from 'bcryptjs';

async function seed() {
  const dataSource = new DataSource(dataSourceOptions);
  await dataSource.initialize();

  console.log('🌱 Starting database seeding...');

  // Create admin user
  const userRepository = dataSource.getRepository(User);
  const adminExists = await userRepository.findOne({
    where: { email: '<EMAIL>' },
  });

  if (!adminExists) {
    const adminUser = userRepository.create({
      email: '<EMAIL>',
      name: 'Admin User',
      phone: '+256700000001',
      password: await bcrypt.hash('admin123', 10),
      role: UserRole.ADMIN,
    });
    await userRepository.save(adminUser);
    console.log('✅ Admin user created: <EMAIL> / admin123');
  }

  // Create test user
  const testUserExists = await userRepository.findOne({
    where: { email: '<EMAIL>' },
  });

  if (!testUserExists) {
    const testUser = userRepository.create({
      email: '<EMAIL>',
      name: 'Test User',
      phone: '+256700000002',
      password: await bcrypt.hash('user123', 10),
      role: UserRole.USER,
    });
    await userRepository.save(testUser);
    console.log('✅ Test user created: <EMAIL> / user123');
  }

  // Create products
  const productRepository = dataSource.getRepository(Product);
  const existingProducts = await productRepository.count();

  if (existingProducts === 0) {
    const products = [
      {
        name: 'Classic White T-Shirt',
        description: 'Comfortable cotton t-shirt perfect for everyday wear',
        price: 29.99,
        category: 'clothing',
        imageUrl: 'https://images.pexels.com/photos/1020585/pexels-photo-1020585.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 50,
      },
      {
        name: 'Denim Jacket',
        description: 'Stylish denim jacket with a modern fit',
        price: 89.99,
        category: 'clothing',
        imageUrl: 'https://images.pexels.com/photos/1124465/pexels-photo-1124465.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 25,
      },
      {
        name: 'Leather Handbag',
        description: 'Premium leather handbag with spacious compartments',
        price: 159.99,
        category: 'accessories',
        imageUrl: 'https://images.pexels.com/photos/1152077/pexels-photo-1152077.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 15,
      },
      {
        name: 'Running Shoes',
        description: 'Lightweight running shoes for maximum comfort',
        price: 119.99,
        category: 'shoes',
        imageUrl: 'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 40,
      },
      {
        name: 'Gold Necklace',
        description: 'Elegant gold necklace with pendant',
        price: 299.99,
        category: 'jewelry',
        imageUrl: 'https://images.pexels.com/photos/1454169/pexels-photo-1454169.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 8,
      },
      {
        name: 'Summer Dress',
        description: 'Flowing summer dress perfect for warm weather',
        price: 69.99,
        category: 'clothing',
        imageUrl: 'https://images.pexels.com/photos/1462637/pexels-photo-1462637.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 30,
      },
      {
        name: 'Sunglasses',
        description: 'Stylish sunglasses with UV protection',
        price: 49.99,
        category: 'accessories',
        imageUrl: 'https://images.pexels.com/photos/1834386/pexels-photo-1834386.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 60,
      },
      {
        name: 'Casual Sneakers',
        description: 'Comfortable casual sneakers for daily wear',
        price: 79.99,
        category: 'shoes',
        imageUrl: 'https://images.pexels.com/photos/1478442/pexels-photo-1478442.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 35,
      },
      {
        name: 'Silk Scarf',
        description: 'Luxurious silk scarf with elegant patterns',
        price: 39.99,
        category: 'accessories',
        imageUrl: 'https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 20,
      },
      {
        name: 'Wool Sweater',
        description: 'Cozy wool sweater for cold weather',
        price: 89.99,
        category: 'clothing',
        imageUrl: 'https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg?auto=compress&cs=tinysrgb&w=400',
        stock: 25,
      },
    ];

    for (const productData of products) {
      const product = productRepository.create(productData);
      await productRepository.save(product);
    }
    console.log('✅ Products created');
  }

  // Create delivery zones
  const deliveryZoneRepository = dataSource.getRepository(DeliveryZone);
  const existingZones = await deliveryZoneRepository.count();

  if (existingZones === 0) {
    const zones = [
      {
        name: 'Central Kampala',
        fee: 5.00,
        centerLatitude: 0.3476,
        centerLongitude: 32.5825,
        radius: 5.0,
        isActive: true,
      },
      {
        name: 'Entebbe',
        fee: 15.00,
        centerLatitude: 0.0514,
        centerLongitude: 32.4634,
        radius: 10.0,
        isActive: true,
      },
      {
        name: 'Mukono',
        fee: 12.00,
        centerLatitude: 0.3533,
        centerLongitude: 32.7553,
        radius: 8.0,
        isActive: true,
      },
      {
        name: 'Wakiso',
        fee: 8.00,
        centerLatitude: 0.4044,
        centerLongitude: 32.4597,
        radius: 12.0,
        isActive: true,
      },
    ];

    for (const zoneData of zones) {
      const zone = deliveryZoneRepository.create(zoneData);
      await deliveryZoneRepository.save(zone);
    }
    console.log('✅ Delivery zones created');
  }

  await dataSource.destroy();
  console.log('🎉 Database seeding completed!');
  console.log('\n📋 Test Accounts:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('User: <EMAIL> / user123');
}

seed().catch((error) => {
  console.error('❌ Seeding failed:', error);
  process.exit(1);
});