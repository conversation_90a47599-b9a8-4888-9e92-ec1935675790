import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { Api<PERSON><PERSON>s, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { DeliveryZonesService } from './delivery-zones.service';
import { CreateDeliveryZoneDto } from './dto/create-delivery-zone.dto';
import { UpdateDeliveryZoneDto } from './dto/update-delivery-zone.dto';
import { CalculateDeliveryFeeDto } from './dto/calculate-delivery-fee.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';

@ApiTags('Delivery Zones')
@Controller('delivery-zones')
export class DeliveryZonesController {
  constructor(private readonly deliveryZonesService: DeliveryZonesService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create delivery zone (Admin only)' })
  create(@Body() createDeliveryZoneDto: CreateDeliveryZoneDto) {
    return this.deliveryZonesService.create(createDeliveryZoneDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all active delivery zones' })
  findAll() {
    return this.deliveryZonesService.findAll();
  }

  @Post('calculate')
  @ApiOperation({ summary: 'Calculate delivery fee for coordinates' })
  calculateDeliveryFee(@Body() calculateDeliveryFeeDto: CalculateDeliveryFeeDto) {
    return this.deliveryZonesService.calculateDeliveryFee(
      calculateDeliveryFeeDto.latitude,
      calculateDeliveryFeeDto.longitude,
    );
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get delivery zone by ID (Admin only)' })
  findOne(@Param('id') id: string) {
    return this.deliveryZonesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update delivery zone (Admin only)' })
  update(@Param('id') id: string, @Body() updateDeliveryZoneDto: UpdateDeliveryZoneDto) {
    return this.deliveryZonesService.update(id, updateDeliveryZoneDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete delivery zone (Admin only)' })
  remove(@Param('id') id: string) {
    return this.deliveryZonesService.remove(id);
  }
}