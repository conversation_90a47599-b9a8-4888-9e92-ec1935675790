import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeliveryZone } from './entities/delivery-zone.entity';
import { CreateDeliveryZoneDto } from './dto/create-delivery-zone.dto';
import { UpdateDeliveryZoneDto } from './dto/update-delivery-zone.dto';

@Injectable()
export class DeliveryZonesService {
  constructor(
    @InjectRepository(DeliveryZone)
    private deliveryZonesRepository: Repository<DeliveryZone>,
  ) {}

  async create(createDeliveryZoneDto: CreateDeliveryZoneDto): Promise<DeliveryZone> {
    const zone = this.deliveryZonesRepository.create(createDeliveryZoneDto);
    return this.deliveryZonesRepository.save(zone);
  }

  async findAll(): Promise<DeliveryZone[]> {
    return this.deliveryZonesRepository.find({
      where: { isActive: true },
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<DeliveryZone> {
    const zone = await this.deliveryZonesRepository.findOne({
      where: { id },
    });

    if (!zone) {
      throw new NotFoundException('Delivery zone not found');
    }

    return zone;
  }

  async update(id: string, updateDeliveryZoneDto: UpdateDeliveryZoneDto): Promise<DeliveryZone> {
    const zone = await this.findOne(id);
    await this.deliveryZonesRepository.update(id, updateDeliveryZoneDto);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const zone = await this.findOne(id);
    await this.deliveryZonesRepository.remove(zone);
  }

  async calculateDeliveryFee(latitude: number, longitude: number): Promise<{ fee: number; zone: string }> {
    const zones = await this.findAll();
    
    for (const zone of zones) {
      const distance = this.calculateDistance(
        latitude,
        longitude,
        zone.centerLatitude,
        zone.centerLongitude,
      );

      if (distance <= zone.radius) {
        return {
          fee: zone.fee,
          zone: zone.name,
        };
      }
    }

    // Default fee for areas outside defined zones
    return {
      fee: 10.00, // Default delivery fee
      zone: 'Standard Delivery',
    };
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}