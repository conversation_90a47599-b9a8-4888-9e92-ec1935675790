import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsBoolean, IsOptional, Min } from 'class-validator';

export class CreateDeliveryZoneDto {
  @ApiProperty({ example: 'Central Kampala' })
  @IsString()
  name: string;

  @ApiProperty({ example: 5.00 })
  @IsNumber()
  @Min(0)
  fee: number;

  @ApiProperty({ example: 0.3476 })
  @IsNumber()
  centerLatitude: number;

  @ApiProperty({ example: 32.5825 })
  @IsNumber()
  centerLongitude: number;

  @ApiProperty({ example: 5.0, description: 'Radius in kilometers' })
  @IsNumber()
  @Min(0)
  radius: number;

  @ApiProperty({ example: true, required: false })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}