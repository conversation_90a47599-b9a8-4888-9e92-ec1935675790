import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('delivery_zones')
export class DeliveryZone {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column('decimal', { precision: 10, scale: 2 })
  fee: number;

  @Column('decimal', { precision: 10, scale: 8 })
  centerLatitude: number;

  @Column('decimal', { precision: 11, scale: 8 })
  centerLongitude: number;

  @Column('decimal', { precision: 10, scale: 2 })
  radius: number; // in kilometers

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}