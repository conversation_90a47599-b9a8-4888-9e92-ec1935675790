import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { DeliveryService } from './delivery.service';
import { CreateDeliveryDto, UpdateDeliveryStatusDto, UpdateLocationDto } from './dto/create-delivery.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';

@ApiTags('Delivery')
@ApiBearerAuth()
@Controller('delivery')
@UseGuards(JwtAuthGuard)
export class DeliveryController {
  constructor(private readonly deliveryService: DeliveryService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new delivery (Admin only)' })
  create(@Body() createDeliveryDto: CreateDeliveryDto) {
    return this.deliveryService.create(createDeliveryDto);
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all deliveries (Admin only)' })
  findAll() {
    return this.deliveryService.findAll();
  }

  @Get('active')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get active deliveries (Admin only)' })
  getActiveDeliveries() {
    return this.deliveryService.getActiveDeliveries();
  }

  @Get('my-deliveries')
  @ApiOperation({ summary: 'Get current user delivery history' })
  getMyDeliveries(@Request() req) {
    return this.deliveryService.getDeliveryHistory(req.user.id);
  }

  @Get('driver/:driverId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get deliveries by driver ID (Admin only)' })
  findByDriverId(@Param('driverId') driverId: string) {
    return this.deliveryService.findByDriverId(driverId);
  }

  @Get('order/:orderId')
  @ApiOperation({ summary: 'Get delivery by order ID' })
  findByOrderId(@Param('orderId') orderId: string, @Request() req) {
    // Users can only view their own order deliveries, admins can view all
    return this.deliveryService.findByOrderId(orderId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get delivery by ID' })
  findOne(@Param('id') id: string) {
    return this.deliveryService.findOne(id);
  }

  @Get(':id/tracking')
  @ApiOperation({ summary: 'Get delivery tracking information' })
  getDeliveryTracking(@Param('id') id: string) {
    return this.deliveryService.getDeliveryTracking(id);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update delivery status' })
  updateStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateDeliveryStatusDto,
    @Request() req
  ) {
    // Only admins and the assigned driver can update status
    return this.deliveryService.updateStatus(id, updateStatusDto);
  }

  @Patch(':id/location')
  @ApiOperation({ summary: 'Update delivery location (Driver only)' })
  updateLocation(
    @Param('id') id: string,
    @Body() updateLocationDto: UpdateLocationDto,
    @Request() req
  ) {
    // Only the assigned driver can update location
    return this.deliveryService.updateLocation(id, updateLocationDto);
  }
}
