import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Delivery, DeliveryStatus } from './entities/delivery.entity';
import { CreateDeliveryDto, UpdateDeliveryStatusDto, UpdateLocationDto } from './dto/create-delivery.dto';
import { Order } from '../orders/entities/order.entity';
import { User } from '../users/entities/user.entity';

@Injectable()
export class DeliveryService {
  constructor(
    @InjectRepository(Delivery)
    private deliveryRepository: Repository<Delivery>,
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async create(createDeliveryDto: CreateDeliveryDto): Promise<Delivery> {
    // Verify order exists
    const order = await this.orderRepository.findOne({
      where: { id: createDeliveryDto.orderId },
    });
    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Verify driver exists and has driver role
    const driver = await this.userRepository.findOne({
      where: { id: createDeliveryDto.driverId },
    });
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // Check if delivery already exists for this order
    const existingDelivery = await this.deliveryRepository.findOne({
      where: { orderId: createDeliveryDto.orderId },
    });
    if (existingDelivery) {
      throw new BadRequestException('Delivery already exists for this order');
    }

    const delivery = this.deliveryRepository.create({
      ...createDeliveryDto,
      estimatedDeliveryTime: createDeliveryDto.estimatedDeliveryTime 
        ? new Date(createDeliveryDto.estimatedDeliveryTime)
        : this.calculateEstimatedDeliveryTime(
            createDeliveryDto.pickupLatitude,
            createDeliveryDto.pickupLongitude,
            createDeliveryDto.deliveryLatitude,
            createDeliveryDto.deliveryLongitude
          ),
    });

    return this.deliveryRepository.save(delivery);
  }

  async findAll(): Promise<Delivery[]> {
    return this.deliveryRepository.find({
      relations: ['order', 'driver'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Delivery> {
    const delivery = await this.deliveryRepository.findOne({
      where: { id },
      relations: ['order', 'driver'],
    });

    if (!delivery) {
      throw new NotFoundException('Delivery not found');
    }

    return delivery;
  }

  async findByOrderId(orderId: string): Promise<Delivery> {
    const delivery = await this.deliveryRepository.findOne({
      where: { orderId },
      relations: ['order', 'driver'],
    });

    if (!delivery) {
      throw new NotFoundException('Delivery not found for this order');
    }

    return delivery;
  }

  async findByDriverId(driverId: string): Promise<Delivery[]> {
    return this.deliveryRepository.find({
      where: { driverId },
      relations: ['order', 'driver'],
      order: { createdAt: 'DESC' },
    });
  }

  async updateStatus(id: string, updateStatusDto: UpdateDeliveryStatusDto): Promise<Delivery> {
    const delivery = await this.findOne(id);

    // Update status and related fields
    delivery.status = updateStatusDto.status;
    
    if (updateStatusDto.currentLatitude && updateStatusDto.currentLongitude) {
      delivery.currentLatitude = updateStatusDto.currentLatitude;
      delivery.currentLongitude = updateStatusDto.currentLongitude;
    }

    if (updateStatusDto.notes) {
      delivery.notes = updateStatusDto.notes;
    }

    if (updateStatusDto.failureReason) {
      delivery.failureReason = updateStatusDto.failureReason;
    }

    // Set timestamps based on status
    const now = new Date();
    switch (updateStatusDto.status) {
      case DeliveryStatus.PICKED_UP:
        delivery.pickedUpAt = now;
        break;
      case DeliveryStatus.DELIVERED:
        delivery.deliveredAt = now;
        break;
    }

    return this.deliveryRepository.save(delivery);
  }

  async updateLocation(id: string, updateLocationDto: UpdateLocationDto): Promise<Delivery> {
    const delivery = await this.findOne(id);

    delivery.currentLatitude = updateLocationDto.latitude;
    delivery.currentLongitude = updateLocationDto.longitude;

    return this.deliveryRepository.save(delivery);
  }

  async getDeliveryTracking(id: string): Promise<{
    delivery: Delivery;
    estimatedTimeRemaining: number;
    distanceRemaining: number;
  }> {
    const delivery = await this.findOne(id);

    const estimatedTimeRemaining = this.calculateEstimatedTimeRemaining(delivery);
    const distanceRemaining = this.calculateDistanceRemaining(delivery);

    return {
      delivery,
      estimatedTimeRemaining,
      distanceRemaining,
    };
  }

  async getActiveDeliveries(): Promise<Delivery[]> {
    return this.deliveryRepository.find({
      where: [
        { status: DeliveryStatus.ASSIGNED },
        { status: DeliveryStatus.PICKED_UP },
        { status: DeliveryStatus.IN_TRANSIT },
      ],
      relations: ['order', 'driver'],
      order: { createdAt: 'DESC' },
    });
  }

  async getDeliveryHistory(userId: string): Promise<Delivery[]> {
    return this.deliveryRepository
      .createQueryBuilder('delivery')
      .leftJoinAndSelect('delivery.order', 'order')
      .leftJoinAndSelect('delivery.driver', 'driver')
      .leftJoinAndSelect('order.user', 'user')
      .where('user.id = :userId', { userId })
      .orderBy('delivery.createdAt', 'DESC')
      .getMany();
  }

  private calculateEstimatedDeliveryTime(
    pickupLat: number,
    pickupLng: number,
    deliveryLat: number,
    deliveryLng: number
  ): Date {
    // Calculate distance and estimate delivery time
    const distance = this.calculateDistance(pickupLat, pickupLng, deliveryLat, deliveryLng);
    
    // Assume average speed of 30 km/h in city traffic
    const averageSpeed = 30; // km/h
    const estimatedHours = distance / averageSpeed;
    
    // Add 15 minutes for pickup time
    const totalMinutes = (estimatedHours * 60) + 15;
    
    const estimatedTime = new Date();
    estimatedTime.setMinutes(estimatedTime.getMinutes() + totalMinutes);
    
    return estimatedTime;
  }

  private calculateEstimatedTimeRemaining(delivery: Delivery): number {
    if (delivery.status === DeliveryStatus.DELIVERED) {
      return 0;
    }

    if (!delivery.estimatedDeliveryTime) {
      return 0;
    }

    const now = new Date();
    const timeRemaining = delivery.estimatedDeliveryTime.getTime() - now.getTime();
    
    return Math.max(0, Math.floor(timeRemaining / (1000 * 60))); // Return minutes
  }

  private calculateDistanceRemaining(delivery: Delivery): number {
    if (delivery.status === DeliveryStatus.DELIVERED) {
      return 0;
    }

    let fromLat = delivery.currentLatitude || delivery.pickupLatitude;
    let fromLng = delivery.currentLongitude || delivery.pickupLongitude;

    return this.calculateDistance(
      fromLat,
      fromLng,
      delivery.deliveryLatitude,
      delivery.deliveryLongitude
    );
  }

  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    // Haversine formula to calculate distance between two points
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c; // Distance in kilometers
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}
