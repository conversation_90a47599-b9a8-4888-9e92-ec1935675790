import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, IsEnum, IsDateString } from 'class-validator';
import { DeliveryStatus } from '../entities/delivery.entity';

export class CreateDeliveryDto {
  @ApiProperty({ example: 'order-uuid' })
  @IsString()
  orderId: string;

  @ApiProperty({ example: 'driver-uuid' })
  @IsString()
  driverId: string;

  @ApiProperty({ example: 0.3476 })
  @IsNumber()
  pickupLatitude: number;

  @ApiProperty({ example: 32.5825 })
  @IsNumber()
  pickupLongitude: number;

  @ApiProperty({ example: '123 Restaurant Street, Kampala' })
  @IsString()
  pickupAddress: string;

  @ApiProperty({ example: 0.3136 })
  @IsNumber()
  deliveryLatitude: number;

  @ApiProperty({ example: 32.5811 })
  @IsNumber()
  deliveryLongitude: number;

  @ApiProperty({ example: '456 Customer Avenue, Kampala' })
  @IsString()
  deliveryAddress: string;

  @ApiProperty({ example: '2024-01-01T12:00:00Z', required: false })
  @IsOptional()
  @IsDateString()
  estimatedDeliveryTime?: string;

  @ApiProperty({ example: 'Handle with care', required: false })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateDeliveryStatusDto {
  @ApiProperty({ enum: DeliveryStatus })
  @IsEnum(DeliveryStatus)
  status: DeliveryStatus;

  @ApiProperty({ example: 0.3476, required: false })
  @IsOptional()
  @IsNumber()
  currentLatitude?: number;

  @ApiProperty({ example: 32.5825, required: false })
  @IsOptional()
  @IsNumber()
  currentLongitude?: number;

  @ApiProperty({ example: 'Package delivered successfully', required: false })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({ example: 'Customer not available', required: false })
  @IsOptional()
  @IsString()
  failureReason?: string;
}

export class UpdateLocationDto {
  @ApiProperty({ example: 0.3476 })
  @IsNumber()
  latitude: number;

  @ApiProperty({ example: 32.5825 })
  @IsNumber()
  longitude: number;
}
