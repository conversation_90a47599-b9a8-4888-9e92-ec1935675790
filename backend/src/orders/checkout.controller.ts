import {
  <PERSON>,
  Post,
  Body,
  UseGuards,
  Request,
  Get,
  Param,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CheckoutService, CheckoutDto, CheckoutItem } from './checkout.service';

@ApiTags('checkout')
@ApiBearerAuth()
@Controller('checkout')
@UseGuards(JwtAuthGuard)
export class CheckoutController {
  constructor(private readonly checkoutService: CheckoutService) {}

  @Post()
  @ApiOperation({ summary: 'Process checkout and create order with payment' })
  async processCheckout(@Request() req, @Body() checkoutData: CheckoutDto) {
    return this.checkoutService.processCheckout(req.user.sub, checkoutData);
  }

  @Post('summary')
  @ApiOperation({ summary: 'Get order summary without creating order' })
  async getOrderSummary(@Request() req, @Body() items: CheckoutItem[]) {
    return this.checkoutService.getOrderSummary(req.user.sub, items);
  }
}
