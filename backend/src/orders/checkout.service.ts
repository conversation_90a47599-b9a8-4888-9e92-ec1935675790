import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from './entities/order.entity';
import { Product } from '../products/entities/product.entity';
import { Address } from '../addresses/entities/address.entity';
import { DeliveryZone } from '../delivery-zones/entities/delivery-zone.entity';
import { PaymentsService } from '../payments/payments.service';
import { OrderStatus } from '../common/enums/order-status.enum';

export interface CheckoutItem {
  productId: string;
  quantity: number;
}

export interface CheckoutDto {
  items: CheckoutItem[];
  addressId?: string;
  deliveryType: 'pickup' | 'delivery';
  paymentMethod: 'mtn' | 'airtel';
  phoneNumber: string;
  notes?: string;
}

@Injectable()
export class CheckoutService {
  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Address)
    private addressRepository: Repository<Address>,
    @InjectRepository(DeliveryZone)
    private deliveryZoneRepository: Repository<DeliveryZone>,
    private paymentsService: PaymentsService,
  ) {}

  async processCheckout(userId: string, checkoutData: CheckoutDto) {
    // Validate items and calculate totals
    const { items, totalAmount, deliveryFee } = await this.validateAndCalculateOrder(
      checkoutData.items,
      checkoutData.deliveryType,
      checkoutData.addressId,
    );

    // Validate address if delivery
    let address = null;
    if (checkoutData.deliveryType === 'delivery') {
      if (!checkoutData.addressId) {
        throw new BadRequestException('Address is required for delivery orders');
      }
      
      address = await this.addressRepository.findOne({
        where: { id: checkoutData.addressId, userId },
      });

      if (!address) {
        throw new NotFoundException('Address not found');
      }
    }

    const finalAmount = totalAmount + deliveryFee;

    // Create order
    const order = this.orderRepository.create({
      userId,
      items,
      totalAmount,
      deliveryFee,
      finalAmount,
      status: OrderStatus.PENDING,
      deliveryType: checkoutData.deliveryType,
      addressId: checkoutData.addressId,
      paymentMethod: checkoutData.paymentMethod,
      notes: checkoutData.notes,
    });

    const savedOrder = await this.orderRepository.save(order);

    // Initiate payment
    const payment = await this.paymentsService.initiatePayment(
      savedOrder.id,
      finalAmount,
      checkoutData.paymentMethod,
      checkoutData.phoneNumber,
    );

    return {
      success: true,
      data: {
        order: savedOrder,
        payment,
        summary: {
          totalAmount,
          deliveryFee,
          finalAmount,
          itemCount: items.length,
        },
      },
    };
  }

  private async validateAndCalculateOrder(
    checkoutItems: CheckoutItem[],
    deliveryType: 'pickup' | 'delivery',
    addressId?: string,
  ) {
    if (!checkoutItems || checkoutItems.length === 0) {
      throw new BadRequestException('Order must contain at least one item');
    }

    // Get all products
    const productIds = checkoutItems.map(item => item.productId);
    const products = await this.productRepository.findByIds(productIds);

    if (products.length !== productIds.length) {
      throw new BadRequestException('Some products were not found');
    }

    // Validate stock and calculate total
    const items = [];
    let totalAmount = 0;

    for (const checkoutItem of checkoutItems) {
      const product = products.find(p => p.id === checkoutItem.productId);
      
      if (!product) {
        throw new BadRequestException(`Product ${checkoutItem.productId} not found`);
      }

      if (!product.isAvailable) {
        throw new BadRequestException(`Product ${product.name} is not available`);
      }

      if (product.stock < checkoutItem.quantity) {
        throw new BadRequestException(
          `Insufficient stock for ${product.name}. Available: ${product.stock}, Requested: ${checkoutItem.quantity}`,
        );
      }

      const itemTotal = product.price * checkoutItem.quantity;
      totalAmount += itemTotal;

      items.push({
        productId: product.id,
        product: {
          id: product.id,
          name: product.name,
          price: product.price,
          imageUrl: product.imageUrl,
          category: product.category,
        },
        quantity: checkoutItem.quantity,
        price: product.price,
        total: itemTotal,
      });
    }

    // Calculate delivery fee
    let deliveryFee = 0;
    if (deliveryType === 'delivery' && addressId) {
      deliveryFee = await this.calculateDeliveryFee(addressId);
    }

    return { items, totalAmount, deliveryFee };
  }

  private async calculateDeliveryFee(addressId: string): Promise<number> {
    const address = await this.addressRepository.findOne({
      where: { id: addressId },
    });

    if (!address) {
      throw new NotFoundException('Address not found');
    }

    // Find applicable delivery zone
    const zones = await this.deliveryZoneRepository.find({
      where: { isActive: true },
    });

    for (const zone of zones) {
      const distance = this.calculateDistance(
        address.latitude,
        address.longitude,
        zone.centerLatitude,
        zone.centerLongitude,
      );

      if (distance <= zone.radius) {
        return zone.fee;
      }
    }

    // Default delivery fee if no zone found
    return 10000; // 10,000 UGX
  }

  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c; // Distance in meters
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  async getOrderSummary(userId: string, checkoutData: CheckoutItem[]) {
    const { items, totalAmount, deliveryFee } = await this.validateAndCalculateOrder(
      checkoutData,
      'delivery', // Default to delivery for calculation
    );

    return {
      success: true,
      data: {
        items,
        summary: {
          totalAmount,
          deliveryFee,
          finalAmount: totalAmount + deliveryFee,
          itemCount: items.length,
        },
      },
    };
  }

  async updateStock(orderId: string) {
    const order = await this.orderRepository.findOne({
      where: { id: orderId },
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Update product stock
    for (const item of order.items) {
      await this.productRepository.decrement(
        { id: item.productId },
        'stock',
        item.quantity,
      );
    }
  }
}
