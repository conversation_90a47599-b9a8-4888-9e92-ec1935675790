import { ApiProperty } from '@nestjs/swagger';
import { IsA<PERSON>y, IsString, IsNumber, IsEnum, IsOptional, ValidateNested, Min } from 'class-validator';
import { Type } from 'class-transformer';

class OrderItemDto {
  @ApiProperty({ example: 'product-uuid' })
  @IsString()
  productId: string;

  @ApiProperty({ example: 2 })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({ example: 29.99 })
  @IsNumber()
  @Min(0)
  price: number;
}

export class CreateOrderDto {
  @ApiProperty({ type: [OrderItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderItemDto)
  items: OrderItemDto[];

  @ApiProperty({ enum: ['pickup', 'delivery'] })
  @IsEnum(['pickup', 'delivery'])
  deliveryType: 'pickup' | 'delivery';

  @ApiProperty({ example: 'address-uuid', required: false })
  @IsString()
  @IsOptional()
  addressId?: string;

  @ApiProperty({ enum: ['mtn', 'airtel'] })
  @IsEnum(['mtn', 'airtel'])
  paymentMethod: 'mtn' | 'airtel';
}