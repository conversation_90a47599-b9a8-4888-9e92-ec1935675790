import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from './entities/order.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { ProductsService } from '../products/products.service';
import { AddressesService } from '../addresses/addresses.service';
import { DeliveryZonesService } from '../delivery-zones/delivery-zones.service';
import { OrderStatus } from '../common/enums/order-status.enum';
import { PaymentStatus } from '../common/enums/payment-status.enum';

@Injectable()
export class OrdersService {
  constructor(
    @InjectRepository(Order)
    private ordersRepository: Repository<Order>,
    private productsService: ProductsService,
    private addressesService: AddressesService,
    private deliveryZonesService: DeliveryZonesService,
  ) {}

  async create(userId: string, createOrderDto: CreateOrderDto): Promise<Order> {
    // Validate products and calculate total
    let totalAmount = 0;
    const orderItems = [];

    for (const item of createOrderDto.items) {
      const product = await this.productsService.findOne(item.productId);
      
      if (product.stock < item.quantity) {
        throw new BadRequestException(`Insufficient stock for product: ${product.name}`);
      }

      const itemTotal = item.price * item.quantity;
      totalAmount += itemTotal;

      orderItems.push({
        productId: item.productId,
        product: {
          id: product.id,
          name: product.name,
          price: product.price,
          imageUrl: product.imageUrl,
          category: product.category,
        },
        quantity: item.quantity,
        price: item.price,
      });
    }

    // Calculate delivery fee
    let deliveryFee = 0;
    let address = null;

    if (createOrderDto.deliveryType === 'delivery') {
      if (!createOrderDto.addressId) {
        throw new BadRequestException('Address is required for delivery orders');
      }

      address = await this.addressesService.findById(createOrderDto.addressId);
      const deliveryInfo = await this.deliveryZonesService.calculateDeliveryFee(
        address.latitude,
        address.longitude,
      );
      deliveryFee = deliveryInfo.fee;
    }

    const finalAmount = totalAmount + deliveryFee;

    // Create order
    const order = this.ordersRepository.create({
      userId,
      items: orderItems,
      totalAmount,
      deliveryFee,
      finalAmount,
      deliveryType: createOrderDto.deliveryType,
      addressId: createOrderDto.addressId,
      paymentMethod: createOrderDto.paymentMethod,
    });

    const savedOrder = await this.ordersRepository.save(order);

    // Update product stock
    for (const item of createOrderDto.items) {
      await this.productsService.updateStock(item.productId, item.quantity);
    }

    return this.findOne(savedOrder.id, userId);
  }

  async findAllByUser(userId: string): Promise<Order[]> {
    return this.ordersRepository.find({
      where: { userId },
      relations: ['address'],
      order: { createdAt: 'DESC' },
    });
  }

  async findAll(): Promise<Order[]> {
    return this.ordersRepository.find({
      relations: ['user', 'address'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string, userId?: string): Promise<Order> {
    const whereCondition = userId ? { id, userId } : { id };
    
    const order = await this.ordersRepository.findOne({
      where: whereCondition,
      relations: ['address', 'user'],
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    return order;
  }

  async update(id: string, updateOrderDto: UpdateOrderDto): Promise<Order> {
    const order = await this.findOne(id);
    await this.ordersRepository.update(id, updateOrderDto);
    return this.findOne(id);
  }

  async updateStatus(id: string, status: OrderStatus): Promise<Order> {
    const order = await this.findOne(id);
    await this.ordersRepository.update(id, { status });
    return this.findOne(id);
  }

  async updatePaymentStatus(id: string, paymentStatus: PaymentStatus, paymentReference?: string): Promise<Order> {
    const order = await this.findOne(id);
    const updateData: any = { paymentStatus };
    
    if (paymentReference) {
      updateData.paymentReference = paymentReference;
    }

    if (paymentStatus === PaymentStatus.PAID) {
      updateData.status = OrderStatus.PAID;
    }

    await this.ordersRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const order = await this.findOne(id);
    await this.ordersRepository.remove(order);
  }
}