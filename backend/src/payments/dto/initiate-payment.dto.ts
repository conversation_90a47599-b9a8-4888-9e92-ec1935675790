import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, Matches } from 'class-validator';

export class InitiatePaymentDto {
  @ApiProperty({ example: 'order-uuid' })
  @IsString()
  orderId: string;

  @ApiProperty({ enum: ['mtn', 'airtel'] })
  @IsEnum(['mtn', 'airtel'])
  provider: 'mtn' | 'airtel';

  @ApiProperty({ example: '+************' })
  @IsString()
  @Matches(/^\+256[0-9]{9}$/, {
    message: 'Phone number must be in format +256XXXXXXXXX',
  })
  phoneNumber: string;
}