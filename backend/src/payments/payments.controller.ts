import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { PaymentsService } from './payments.service';
import { InitiatePaymentDto } from './dto/initiate-payment.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';

@ApiTags('Payments')
@ApiBearerAuth()
@Controller('payments')
@UseGuards(JwtAuthGuard)
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post('initiate')
  @ApiOperation({ summary: 'Initiate mobile money payment' })
  initiatePayment(@Body() initiatePaymentDto: InitiatePaymentDto) {
    return this.paymentsService.initiatePayment(initiatePaymentDto);
  }

  @Get('status/:referenceId')
  @ApiOperation({ summary: 'Check payment status by reference ID' })
  checkPaymentStatus(@Param('referenceId') referenceId: string) {
    return this.paymentsService.checkPaymentStatus(referenceId);
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all payments (Admin only)' })
  findAll() {
    return this.paymentsService.findAll();
  }

  @Get('my-payments')
  @ApiOperation({ summary: 'Get current user payments' })
  getMyPayments(@Request() req) {
    return this.paymentsService.findByUserId(req.user.sub);
  }

  @Get('history')
  @ApiOperation({ summary: 'Get payment history for current user' })
  getPaymentHistory(@Request() req) {
    return this.paymentsService.getPaymentHistory(req.user.sub);
  }

  @Get(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get payment by ID (Admin only)' })
  findOne(@Param('id') id: string) {
    return this.paymentsService.findOne(id);
  }
}