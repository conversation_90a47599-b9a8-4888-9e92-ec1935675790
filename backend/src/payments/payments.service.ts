import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Payment } from './entities/payment.entity';
import { OrdersService } from '../orders/orders.service';
import { InitiatePaymentDto } from './dto/initiate-payment.dto';
import { PaymentStatus } from '../common/enums/payment-status.enum';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private paymentsRepository: Repository<Payment>,
    private ordersService: OrdersService,
    private configService: ConfigService,
  ) {}

  async initiatePayment(initiatePaymentDto: InitiatePaymentDto): Promise<Payment> {
    const order = await this.ordersService.findOne(initiatePaymentDto.orderId);
    
    if (!order) {
      throw new NotFoundException('Order not found');
    }

    if (order.paymentStatus === PaymentStatus.PAID) {
      throw new BadRequestException('Order is already paid');
    }

    // Generate unique reference ID
    const referenceId = `TUL-${Date.now()}-${uuidv4().slice(0, 8)}`;

    // Create payment record
    const payment = this.paymentsRepository.create({
      orderId: initiatePaymentDto.orderId,
      provider: initiatePaymentDto.provider,
      phoneNumber: initiatePaymentDto.phoneNumber,
      amount: order.finalAmount,
      referenceId,
      status: PaymentStatus.PENDING,
    });

    const savedPayment = await this.paymentsRepository.save(payment);

    // Initiate payment with provider (mock implementation)
    try {
      const providerResponse = await this.initiateProviderPayment(
        initiatePaymentDto.provider,
        initiatePaymentDto.phoneNumber,
        order.finalAmount,
        referenceId,
      );

      // Update payment with provider response
      savedPayment.externalTransactionId = providerResponse.transactionId;
      savedPayment.providerResponse = providerResponse;
      
      await this.paymentsRepository.save(savedPayment);

      // Update order payment reference
      await this.ordersService.updatePaymentStatus(
        order.id,
        PaymentStatus.PENDING,
        referenceId,
      );

      return savedPayment;
    } catch (error) {
      // Update payment status to failed
      savedPayment.status = PaymentStatus.FAILED;
      savedPayment.providerResponse = { error: error.message };
      await this.paymentsRepository.save(savedPayment);

      await this.ordersService.updatePaymentStatus(order.id, PaymentStatus.FAILED);
      
      throw new BadRequestException(`Payment initiation failed: ${error.message}`);
    }
  }

  async checkPaymentStatus(referenceId: string): Promise<Payment> {
    const payment = await this.paymentsRepository.findOne({
      where: { referenceId },
      relations: ['order'],
    });

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    // Check status with provider (mock implementation)
    if (payment.status === PaymentStatus.PENDING) {
      const providerStatus = await this.checkProviderPaymentStatus(
        payment.provider,
        payment.externalTransactionId,
      );

      if (providerStatus.status === 'success') {
        payment.status = PaymentStatus.PAID;
        await this.paymentsRepository.save(payment);
        
        // Update order status
        await this.ordersService.updatePaymentStatus(
          payment.orderId,
          PaymentStatus.PAID,
          referenceId,
        );
      } else if (providerStatus.status === 'failed') {
        payment.status = PaymentStatus.FAILED;
        await this.paymentsRepository.save(payment);
        
        await this.ordersService.updatePaymentStatus(
          payment.orderId,
          PaymentStatus.FAILED,
        );
      }
    }

    return payment;
  }

  async findAll(): Promise<Payment[]> {
    return this.paymentsRepository.find({
      relations: ['order'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Payment> {
    const payment = await this.paymentsRepository.findOne({
      where: { id },
      relations: ['order'],
    });

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    return payment;
  }

  // Mock implementation for MTN/Airtel payment initiation
  private async initiateProviderPayment(
    provider: 'mtn' | 'airtel',
    phoneNumber: string,
    amount: number,
    referenceId: string,
  ): Promise<any> {
    // This is a mock implementation
    // In production, you would integrate with actual MTN/Airtel APIs
    
    console.log(`Initiating ${provider.toUpperCase()} payment:`, {
      phoneNumber,
      amount,
      referenceId,
    });

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock successful response
    return {
      transactionId: `${provider.toUpperCase()}-${Date.now()}`,
      status: 'pending',
      message: 'Payment request sent to user',
    };
  }

  // Mock implementation for checking payment status
  private async checkProviderPaymentStatus(
    provider: 'mtn' | 'airtel',
    transactionId: string,
  ): Promise<any> {
    // This is a mock implementation
    // In production, you would check with actual MTN/Airtel APIs
    
    console.log(`Checking ${provider.toUpperCase()} payment status:`, transactionId);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock random success/failure for demo purposes
    const isSuccess = Math.random() > 0.3; // 70% success rate for demo
    
    return {
      status: isSuccess ? 'success' : 'pending',
      transactionId,
    };
  }
}