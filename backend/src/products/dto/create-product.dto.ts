import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsUrl, Min } from 'class-validator';

export class CreateProductDto {
  @ApiProperty({ example: 'Classic White T-Shirt' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'Comfortable cotton t-shirt perfect for everyday wear' })
  @IsString()
  description: string;

  @ApiProperty({ example: 29.99 })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({ example: 'clothing' })
  @IsString()
  category: string;

  @ApiProperty({ example: 'https://images.pexels.com/photos/1020585/pexels-photo-1020585.jpeg' })
  @IsUrl()
  imageUrl: string;

  @ApiProperty({ example: 50 })
  @IsNumber()
  @Min(0)
  stock: number;
}