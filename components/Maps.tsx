import React from 'react';
import { View, Text, Platform } from 'react-native';
import { MapPin } from 'lucide-react-native';
import { colors, spacing } from '../constants/colors';

// Platform-specific map components - only import on native platforms
let MapView: any = null;
let Marker: any = null;
let Polyline: any = null;

// Only require react-native-maps on native platforms
if (Platform.OS === 'ios' || Platform.OS === 'android') {
  try {
    const Maps = require('react-native-maps');
    MapView = Maps.default;
    Marker = Maps.Marker;
    Polyline = Maps.Polyline;
  } catch (error) {
    console.warn('react-native-maps not available:', error);
  }
}

interface MapProps {
  style?: any;
  initialRegion?: any;
  region?: any;
  onPress?: (event: any) => void;
  showsUserLocation?: boolean;
  showsMyLocationButton?: boolean;
  children?: React.ReactNode;
}

interface MarkerProps {
  coordinate: {
    latitude: number;
    longitude: number;
  };
  title?: string;
  description?: string;
  pinColor?: string;
  children?: React.ReactNode;
}

interface PolylineProps {
  coordinates: Array<{
    latitude: number;
    longitude: number;
  }>;
  strokeColor?: string;
  strokeWidth?: number;
  lineDashPattern?: number[];
}

// Web fallback component
const WebMapPlaceholder: React.FC<{ style?: any; children?: React.ReactNode }> = ({ 
  style, 
  children 
}) => (
  <View style={[{
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.border,
  }, style]}>
    <MapPin size={48} color={colors.primary} />
    <Text style={{
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginTop: spacing.md,
    }}>
      Map View
    </Text>
    <Text style={{
      fontSize: 14,
      color: colors.textMuted,
      textAlign: 'center',
      marginTop: spacing.sm,
      paddingHorizontal: spacing.lg,
    }}>
      Interactive maps are available on mobile devices
    </Text>
    {children}
  </View>
);

// Exported components
export const Map = React.forwardRef<any, MapProps>((props, ref) => {
  if (Platform.OS !== 'ios' && Platform.OS !== 'android' || !MapView) {
    return <WebMapPlaceholder style={props.style}>{props.children}</WebMapPlaceholder>;
  }

  return <MapView ref={ref} {...props} />;
});

export const MapMarker: React.FC<MarkerProps> = (props) => {
  if (Platform.OS !== 'ios' && Platform.OS !== 'android' || !Marker) {
    return null;
  }

  return <Marker {...props} />;
};

export const MapPolyline: React.FC<PolylineProps> = (props) => {
  if (Platform.OS !== 'ios' && Platform.OS !== 'android' || !Polyline) {
    return null;
  }

  return <Polyline {...props} />;
};

// Default export for backward compatibility
export default Map;
