import AsyncStorage from '@react-native-async-storage/async-storage';
import { ApiResponse, Product, User, Order, Address, Payment } from '../types';

const API_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000';

class ApiService {
  private async getHeaders(): Promise<HeadersInit> {
    const token = await AsyncStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getHeaders();
      const response = await fetch(`${API_URL}${endpoint}`, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Network error' }));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      
      // Handle NestJS response format
      if (data.success !== undefined) {
        return data;
      }

      // Wrap successful responses
      return {
        success: true,
        data: data,
      };
    } catch (error) {
      console.error('API Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  // Auth
  async login(email: string, password: string): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async register(userData: {
    email: string;
    password: string;
    name: string;
    phone: string;
  }): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.request('/users/profile');
  }

  async updateProfile(profileData: {
    name?: string;
    phone?: string;
    avatarUrl?: string;
  }): Promise<ApiResponse<User>> {
    return this.request('/users/profile', {
      method: 'PATCH',
      body: JSON.stringify(profileData),
    });
  }

  async changePassword(passwordData: {
    currentPassword: string;
    newPassword: string;
  }): Promise<ApiResponse<{ message: string }>> {
    return this.request('/users/profile/password', {
      method: 'PATCH',
      body: JSON.stringify(passwordData),
    });
  }

  // Products
  async getProducts(category?: string): Promise<ApiResponse<Product[]>> {
    const query = category ? `?category=${category}` : '';
    return this.request(`/products${query}`);
  }

  async getProduct(id: string): Promise<ApiResponse<Product>> {
    return this.request(`/products/${id}`);
  }

  async getCategories(): Promise<ApiResponse<string[]>> {
    return this.request('/products/categories');
  }

  // Orders
  async createOrder(orderData: {
    items: Array<{ productId: string; quantity: number; price: number }>;
    deliveryType: 'pickup' | 'delivery';
    addressId?: string;
    paymentMethod: 'mtn' | 'airtel';
  }): Promise<ApiResponse<Order>> {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async getOrders(): Promise<ApiResponse<Order[]>> {
    return this.request('/orders');
  }

  async getOrder(id: string): Promise<ApiResponse<Order>> {
    return this.request(`/orders/${id}`);
  }

  // Addresses
  async getAddresses(): Promise<ApiResponse<Address[]>> {
    return this.request('/addresses');
  }

  async createAddress(addressData: Omit<Address, 'id' | 'userId'>): Promise<ApiResponse<Address>> {
    return this.request('/addresses', {
      method: 'POST',
      body: JSON.stringify(addressData),
    });
  }

  // Payments
  async initiatePayment(data: {
    orderId: string;
    provider: 'mtn' | 'airtel';
    phoneNumber: string;
  }): Promise<ApiResponse<Payment>> {
    return this.request('/payments/initiate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async checkPaymentStatus(referenceId: string): Promise<ApiResponse<Payment>> {
    return this.request(`/payments/status/${referenceId}`);
  }

  // Delivery Zones
  async calculateDeliveryFee(latitude: number, longitude: number): Promise<ApiResponse<{ fee: number; zone: string }>> {
    return this.request('/delivery-zones/calculate', {
      method: 'POST',
      body: JSON.stringify({ latitude, longitude }),
    });
  }

  // Admin APIs
  async getDashboardStats(): Promise<ApiResponse<any>> {
    return this.request('/admin/dashboard');
  }

  async getAnalyticsOverview(period: string = '30d'): Promise<ApiResponse<any>> {
    return this.request(`/admin/analytics/overview?period=${period}`);
  }

  async getSalesAnalytics(period: string = '30d'): Promise<ApiResponse<any>> {
    return this.request(`/admin/analytics/sales?period=${period}`);
  }

  async getUserAnalytics(period: string = '30d'): Promise<ApiResponse<any>> {
    return this.request(`/admin/analytics/users?period=${period}`);
  }

  async getProductAnalytics(period: string = '30d'): Promise<ApiResponse<any>> {
    return this.request(`/admin/analytics/products?period=${period}`);
  }

  async searchUsers(query?: string, role?: string, page: number = 1, limit: number = 10): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    if (query) params.append('q', query);
    if (role) params.append('role', role);
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    return this.request(`/admin/users/search?${params.toString()}`);
  }

  async searchOrders(query?: string, status?: string, page: number = 1, limit: number = 10): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    if (query) params.append('q', query);
    if (status) params.append('status', status);
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    return this.request(`/admin/orders/search?${params.toString()}`);
  }

  async searchProducts(query?: string, category?: string, page: number = 1, limit: number = 10): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    if (query) params.append('q', query);
    if (category) params.append('category', category);
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    return this.request(`/admin/products/search?${params.toString()}`);
  }

  async updateUserStatus(id: string, isActive: boolean): Promise<ApiResponse<any>> {
    return this.request(`/admin/users/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ isActive }),
    });
  }

  async getSystemHealth(): Promise<ApiResponse<any>> {
    return this.request('/admin/system/health');
  }

  async createProduct(productData: any): Promise<ApiResponse<any>> {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  }

  async updateProduct(id: string, productData: any): Promise<ApiResponse<any>> {
    return this.request(`/products/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(productData),
    });
  }

  async deleteProduct(id: string): Promise<ApiResponse<any>> {
    return this.request(`/products/${id}`, {
      method: 'DELETE',
    });
  }

  // Delivery APIs
  async getMyDeliveries(): Promise<ApiResponse<any[]>> {
    return this.request('/delivery/my-deliveries');
  }

  async getDeliveryByOrderId(orderId: string): Promise<ApiResponse<any>> {
    return this.request(`/delivery/order/${orderId}`);
  }

  async getDeliveryTracking(deliveryId: string): Promise<ApiResponse<any>> {
    return this.request(`/delivery/${deliveryId}/tracking`);
  }

  async updateDeliveryLocation(deliveryId: string, latitude: number, longitude: number): Promise<ApiResponse<any>> {
    return this.request(`/delivery/${deliveryId}/location`, {
      method: 'PATCH',
      body: JSON.stringify({ latitude, longitude }),
    });
  }

  async updateDeliveryStatus(deliveryId: string, status: string, notes?: string): Promise<ApiResponse<any>> {
    return this.request(`/delivery/${deliveryId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status, notes }),
    });
  }

  async createDelivery(deliveryData: any): Promise<ApiResponse<any>> {
    return this.request('/delivery', {
      method: 'POST',
      body: JSON.stringify(deliveryData),
    });
  }

  async getActiveDeliveries(): Promise<ApiResponse<any[]>> {
    return this.request('/delivery/active');
  }
}

export const apiService = new ApiService();