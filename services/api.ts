import AsyncStorage from '@react-native-async-storage/async-storage';
import { ApiResponse, Product, User, Order, Address, Payment } from '../types';

const API_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000';

class ApiService {
  private async getHeaders(): Promise<HeadersInit> {
    const token = await AsyncStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getHeaders();
      const response = await fetch(`${API_URL}${endpoint}`, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Network error' }));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      
      // Handle NestJS response format
      if (data.success !== undefined) {
        return data;
      }

      // Wrap successful responses
      return {
        success: true,
        data: data,
      };
    } catch (error) {
      console.error('API Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  // Auth
  async login(email: string, password: string): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async register(userData: {
    email: string;
    password: string;
    name: string;
    phone: string;
  }): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.request('/users/profile');
  }

  // Products
  async getProducts(category?: string): Promise<ApiResponse<Product[]>> {
    const query = category ? `?category=${category}` : '';
    return this.request(`/products${query}`);
  }

  async getProduct(id: string): Promise<ApiResponse<Product>> {
    return this.request(`/products/${id}`);
  }

  async getCategories(): Promise<ApiResponse<string[]>> {
    return this.request('/products/categories');
  }

  // Orders
  async createOrder(orderData: {
    items: Array<{ productId: string; quantity: number; price: number }>;
    deliveryType: 'pickup' | 'delivery';
    addressId?: string;
    paymentMethod: 'mtn' | 'airtel';
  }): Promise<ApiResponse<Order>> {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async getOrders(): Promise<ApiResponse<Order[]>> {
    return this.request('/orders');
  }

  async getOrder(id: string): Promise<ApiResponse<Order>> {
    return this.request(`/orders/${id}`);
  }

  // Addresses
  async getAddresses(): Promise<ApiResponse<Address[]>> {
    return this.request('/addresses');
  }

  async createAddress(addressData: Omit<Address, 'id' | 'userId'>): Promise<ApiResponse<Address>> {
    return this.request('/addresses', {
      method: 'POST',
      body: JSON.stringify(addressData),
    });
  }

  // Payments
  async initiatePayment(data: {
    orderId: string;
    provider: 'mtn' | 'airtel';
    phoneNumber: string;
  }): Promise<ApiResponse<Payment>> {
    return this.request('/payments/initiate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async checkPaymentStatus(referenceId: string): Promise<ApiResponse<Payment>> {
    return this.request(`/payments/status/${referenceId}`);
  }

  // Delivery Zones
  async calculateDeliveryFee(latitude: number, longitude: number): Promise<ApiResponse<{ fee: number; zone: string }>> {
    return this.request('/delivery-zones/calculate', {
      method: 'POST',
      body: JSON.stringify({ latitude, longitude }),
    });
  }
}

export const apiService = new ApiService();