import * as Location from 'expo-location';
import { Alert } from 'react-native';

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

export interface LocationWithAddress extends LocationCoordinates {
  address?: string;
}

class LocationService {
  private watchId: Location.LocationSubscription | null = null;
  private currentLocation: Location.LocationObject | null = null;

  /**
   * Request location permissions from the user
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Location Permission Required',
          'This app needs location access to provide delivery tracking and location-based services.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Location.requestForegroundPermissionsAsync() },
          ]
        );
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return false;
    }
  }

  /**
   * Get the current location of the device
   */
  async getCurrentLocation(): Promise<Location.LocationObject | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeInterval: 5000,
        distanceInterval: 10,
      });

      this.currentLocation = location;
      return location;
    } catch (error) {
      console.error('Error getting current location:', error);
      Alert.alert('Location Error', 'Unable to get your current location. Please try again.');
      return null;
    }
  }

  /**
   * Start watching location changes
   */
  async startLocationTracking(
    onLocationUpdate: (location: Location.LocationObject) => void,
    onError?: (error: any) => void
  ): Promise<boolean> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return false;
      }

      // Stop any existing tracking
      await this.stopLocationTracking();

      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 10000, // Update every 10 seconds
          distanceInterval: 50, // Update when moved 50 meters
        },
        (location) => {
          this.currentLocation = location;
          onLocationUpdate(location);
        }
      );

      return true;
    } catch (error) {
      console.error('Error starting location tracking:', error);
      if (onError) {
        onError(error);
      }
      return false;
    }
  }

  /**
   * Stop watching location changes
   */
  async stopLocationTracking(): Promise<void> {
    if (this.watchId) {
      this.watchId.remove();
      this.watchId = null;
    }
  }

  /**
   * Get address from coordinates using reverse geocoding
   */
  async getAddressFromCoordinates(
    latitude: number,
    longitude: number
  ): Promise<string | null> {
    try {
      const addresses = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (addresses && addresses.length > 0) {
        const address = addresses[0];
        const addressParts = [
          address.name,
          address.street,
          address.district,
          address.city,
          address.region,
          address.country,
        ].filter(Boolean);

        return addressParts.join(', ');
      }

      return null;
    } catch (error) {
      console.error('Error getting address from coordinates:', error);
      return null;
    }
  }

  /**
   * Get coordinates from address using geocoding
   */
  async getCoordinatesFromAddress(address: string): Promise<LocationCoordinates | null> {
    try {
      const locations = await Location.geocodeAsync(address);

      if (locations && locations.length > 0) {
        const location = locations[0];
        return {
          latitude: location.latitude,
          longitude: location.longitude,
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting coordinates from address:', error);
      return null;
    }
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  calculateDistance(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c; // Distance in kilometers
  }

  /**
   * Calculate estimated travel time between two coordinates
   */
  calculateEstimatedTravelTime(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number,
    averageSpeed: number = 30 // km/h
  ): number {
    const distance = this.calculateDistance(lat1, lng1, lat2, lng2);
    return (distance / averageSpeed) * 60; // Return time in minutes
  }

  /**
   * Check if location services are enabled
   */
  async isLocationEnabled(): Promise<boolean> {
    try {
      return await Location.hasServicesEnabledAsync();
    } catch (error) {
      console.error('Error checking location services:', error);
      return false;
    }
  }

  /**
   * Get the last known location
   */
  getLastKnownLocation(): Location.LocationObject | null {
    return this.currentLocation;
  }

  /**
   * Format coordinates for display
   */
  formatCoordinates(latitude: number, longitude: number): string {
    const latDirection = latitude >= 0 ? 'N' : 'S';
    const lngDirection = longitude >= 0 ? 'E' : 'W';
    
    return `${Math.abs(latitude).toFixed(6)}°${latDirection}, ${Math.abs(longitude).toFixed(6)}°${lngDirection}`;
  }

  /**
   * Check if coordinates are within Uganda bounds (approximate)
   */
  isWithinUganda(latitude: number, longitude: number): boolean {
    // Approximate bounds for Uganda
    const ugandaBounds = {
      north: 4.234,
      south: -1.484,
      east: 35.036,
      west: 29.573,
    };

    return (
      latitude >= ugandaBounds.south &&
      latitude <= ugandaBounds.north &&
      longitude >= ugandaBounds.west &&
      longitude <= ugandaBounds.east
    );
  }

  /**
   * Get location accuracy description
   */
  getAccuracyDescription(accuracy: number | null): string {
    if (!accuracy) return 'Unknown';
    
    if (accuracy <= 5) return 'Excellent';
    if (accuracy <= 10) return 'Good';
    if (accuracy <= 20) return 'Fair';
    return 'Poor';
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}

export const locationService = new LocationService();
