import AsyncStorage from '@react-native-async-storage/async-storage';
import { CartItem, Product, User, Order } from '../types';

const KEYS = {
  CART: 'cart',
  PRODUCTS: 'products',
  USER: 'user',
  TOKEN: 'token',
  ORDERS: 'orders',
  ADDRESSES: 'addresses',
};

class StorageService {
  // Cart Management
  async getCart(): Promise<CartItem[]> {
    try {
      const cart = await AsyncStorage.getItem(KEYS.CART);
      return cart ? JSON.parse(cart) : [];
    } catch (error) {
      console.error('Error getting cart:', error);
      return [];
    }
  }

  async saveCart(cart: CartItem[]): Promise<void> {
    try {
      await AsyncStorage.setItem(KEYS.CART, JSON.stringify(cart));
    } catch (error) {
      console.error('Error saving cart:', error);
    }
  }

  async clearCart(): Promise<void> {
    try {
      await AsyncStorage.removeItem(KEYS.CART);
    } catch (error) {
      console.error('Error clearing cart:', error);
    }
  }

  // Products Cache
  async cacheProducts(products: Product[]): Promise<void> {
    try {
      await AsyncStorage.setItem(KEYS.PRODUCTS, JSON.stringify(products));
    } catch (error) {
      console.error('Error caching products:', error);
    }
  }

  async getCachedProducts(): Promise<Product[]> {
    try {
      const products = await AsyncStorage.getItem(KEYS.PRODUCTS);
      return products ? JSON.parse(products) : [];
    } catch (error) {
      console.error('Error getting cached products:', error);
      return [];
    }
  }

  // User & Auth
  async saveUser(user: User): Promise<void> {
    try {
      await AsyncStorage.setItem(KEYS.USER, JSON.stringify(user));
    } catch (error) {
      console.error('Error saving user:', error);
    }
  }

  async getUser(): Promise<User | null> {
    try {
      const user = await AsyncStorage.getItem(KEYS.USER);
      return user ? JSON.parse(user) : null;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  }

  async saveToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem(KEYS.TOKEN, token);
    } catch (error) {
      console.error('Error saving token:', error);
    }
  }

  async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(KEYS.TOKEN);
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  }

  async clearAuth(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([KEYS.USER, KEYS.TOKEN]);
    } catch (error) {
      console.error('Error clearing auth:', error);
    }
  }

  // Orders Cache
  async cacheOrders(orders: Order[]): Promise<void> {
    try {
      await AsyncStorage.setItem(KEYS.ORDERS, JSON.stringify(orders));
    } catch (error) {
      console.error('Error caching orders:', error);
    }
  }

  async getCachedOrders(): Promise<Order[]> {
    try {
      const orders = await AsyncStorage.getItem(KEYS.ORDERS);
      return orders ? JSON.parse(orders) : [];
    } catch (error) {
      console.error('Error getting cached orders:', error);
      return [];
    }
  }

  // General Storage
  async setItem(key: string, value: any): Promise<void> {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting item ${key}:`, error);
    }
  }

  async getItem<T>(key: string): Promise<T | null> {
    try {
      const value = await AsyncStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Error getting item ${key}:`, error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing item ${key}:`, error);
    }
  }

  async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  }
}

export const storageService = new StorageService();